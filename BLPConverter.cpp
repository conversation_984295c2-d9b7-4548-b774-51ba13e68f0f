/*
BLPConverter.cpp - Converts BLP files to TGA and back.
See the Usage() function for info on using this application.

///////////////////////////////////////////////////////////////////////////////

	(C) 2008 <PERSON> (<EMAIL>)

	This program is free software; you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation; either version 2 of the License, or
	(at your option) any later version.

	This program is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with this program; if not, write to the Free Software
	Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA  02110-1301  USA

///////////////////////////////////////////////////////////////////////////////

This program requires quite a few things in order to compile correctly:

- Windows 2000 and up.  I'm unsure about the various libraries but at the very
  least this file calls win32 functions to handle input wildcards.
- palbmp: A pallete library (C) Charles Bloom.  http://www.cbloom.co/m
	I'm opting to comply with section 0 of the Bloom Public License: I'm
	distributing this program using the GPL.  I've also made some very small
	changes to it: I commented out the stderr spam.
- crblib: Another library (C) Charles Bloom, which is a dependency of palbmp.
- libpng: The offical PNG reference library. http://www.libpng.org/
- squish: A DXT compression library.  http://www.sjbrown.co.uk/?code=squish

///////////////////////////////////////////////////////////////////////////////

Known Issues:
- Saving pal/alpha to PNG w/ alpha mask doesn't work right.  The moved palette colors don't get updated in the image
array properly.  Noticible when making Mip test image.

///////////////////////////////////////////////////////////////////////////////

Versions:

* 8.4 *
- Added support for BLP_RBGAs.
- Expanded return codes.  Instead of always -1 on error, now a variety of
  negative numbers are possible.

* 8.3 *
- Added support for reading BLPs of type "Type 1 Encoding 2 AlphaDepth 4
  AlphaEncoding 1 (DXT3 four bits alpha)". Note that the program treats them
  exactly the same as AlphaDepth 8 since there is only one type of DXT3
  compression (that I know of).

* 8.2 *
- Fixed output filename bug when using wildcards.

* 8.1 *
- Added support for BLP_PAL_A4 format.

* 8 *
- Incorporated David Holland's (<EMAIL>) changes no allow for Linux-compatible builds.
- Using squish instead of the ATIlib for DXT compression, as it is cross platform.

* 7 *
- Textures that have 256 or fewer unique colors now use exactly those colors when palettized.
- Made -r option default.  -s turns it off.

* 6 *
- Support for decoding DXT5 BLPs.
- Improved the way mip level are created for images with alpha.
- Fixed bug which could cause palettized BLPs with alpha to not have their alpha converted correctly. (Occured when the first transparent pixel uses palette entry zero.)

* 5 *
- Fixed bug which was causing -g value to be applied twice when using -r and converting a RGBA PNG to a BLP.
- -g will now work when when converting a Palettized PNG that has no alpha.
- Added -h, which forces cHaracter format BLPs. (palettized, no alpha).

* 4 *
- Added -e and -n options.
- -g option now works when doing a rgb png -> p8 blp.
- -g option will automatically create an alpha channel for RGB source PNGs.
- Removed palettization spam.
- -m option now supports wildcards, ie. *.blp.

* 3 *
- Removed dependency on DirectX.
- Added -m, -i, and -c options.

* 2 *
- Fixed bug preventing files from being processed if there was a '.' in their path.
- Now setting device format to current display format, in hopes of fixing an error
that people are reporting when the program tries to call CreateDevice().

* 1 *
- Initial release


///////////////////////////////////////////////////////////////////////////////

Personal Notes:

*** PNG -> BLP ***
- P8 -> uncompressed, 0 bit alpha
- P8 with transparency -> uncompressed, 8bit alpha  (Note: Don't know how to make PNGs of this type, except for transparency count = 1)
- RGB -> compressed, DXT1
- RGBA	-> compressed, DXT3
	*OR*
		-> P8 with transparency [Use this option to preserve BLP format ]

*** BLP -> PNG ***
- uncompressed, 0 bit alpha -> P8
	Ex. Character Skins.
- uncompressed, 1 bit alpha -> P8 with alpha palette (1 entry)
	Ex. Some clothing (Deckhand's shirt for example)
- uncompressed, 8 bit alpha -> P8 with alpha palette (1 entry) [This is what WowImage does: retains palette, destroys alpha detail.]
	*OR*
							-> RGBA [Destroys palette, retains alpha detail.]
	Ex. Most clothing.  Item\TextureComponents\TorsoUpperTexture\Cloth_Horde_C_01Purple_Chest_TU_U.png for example.

  - compressed, 0 bit alpha -> DXT1
	Ex. Sansam root
- compressed, 1 bit alpha -> DXT1
	Ex. peaceflower
- compressed, 8 bit alpha -> DXT3
	Ex. sungrass


uncomp 1-bit:
- Deckhand's shirt (Cloth_A_03Crewman_Chest_TU_F.blp).  WowImage doesn't give an error when trying to decode this blp
but doesn't create a png, either.

Notes:
09-12-24: Added support for BLP_PAL_A4.
08-02-10: Removed SQUISH_USE_SSE=2 predefine from squish project.

Test:			| Source		| -			| -s
______________________________________________________________
BLP_U_A0		| char skin		| OK/OK					HumanMaleSkin00_00.blp
BLP_U_A1		| some clothes	| OK/OK					?
BLP_U_A4		| some clothes	| OK/OK					Plate_RaidWarrior_H_01_Chest_TL_F.blp (completely opaque, though)
BLP_U_A8		| clothes		| OK/OK					Leather_A_05Yellow_Chest_TU_M.blp (human male war starting upper shirt)
BLP_C_A0		| earthroot		| OK					Sword_1H_Short_A_02Rusty.blp (human male war starting sword)
BLP_C_A1		| peacebloom	| OK					Hair01_05 (Human golden blonde hair)
BLP_DXT3		| 				| OK					NorthshireValley1.blp | sungrass
BLP_C_A8_7		| see end ->	|						Ability_Druid_Cyclone.blp

PNG_RGB			|				| BLP_U_A0	| BLP_C_A0 (DXT1)
PNG_RGBA		|				| BLP_U_A8	| BLP_C_A8 (DXT3)
PNG_P8			|				| BLP_U_A0	| -
PNG_P8_1trans	|				| BLP_U_A8


PNG_RGB			| ok, igok (pig)
PNG_RGBA			| ok, igok (horde armory sign)
PNG_RGBA	-> P A8	| ok, igok (fang top)
PNG_P8			| ok, igok (character skin)
PNG_P8_1trans	| ok, igok (fang top)

 */

#ifndef _linux_
#include <conio.h>
#else
#include "port.h"
#endif

///////////////////////////////////////////////////////////////////////////////
// Windows Stuff

#ifdef _linux_
#include <libgen.h>
#include <sys/types.h>
#include <dirent.h>
#include <fnmatch.h>
#include <png.h>
#include <errno.h>
#include "palbmp/palcreate.h"
#include "palbmp/palettize.h"
#endif
#include "MemImage.h"
///////////////////////////////////////////////////////////////////////////////
// User options.
bool g_bInfoMode = false;
FORMATID g_targetFormatID = FORMAT_UNSPECIFIED;

///////////////////////////////////////////////////////////////////////////////
// Testing code.

const char *pngTestList[] =
	{
		"PNG_RGB",
		"PNG_RGBA",
		"PNG_P8",
		"PNG_P8_1Trans",
};

const char *blpTestList[] =
	{
		"BLP_U_A0",
		"BLP_U_A1",
		"BLP_U_A8",
		"BLP_DXT1",
		"BLP_DXT1_A1",
		"BLP_DXT3",
		"BLP_DXT5"};

static bool RunTest()
{
	MemImage aMemImage;

	/*
	// PNG->BLP test.
	for (int iFile = 0; iFile < 4; ++iFile)
	{
		std::string inputFilename = std::string(pngTestList[iFile]) + ".png";
		if (!aMemImage.LoadFromPNG(inputFilename))
			return false;

		for (int iFormat = 1; iFormat < BLPTYPE_COUNT; ++iFormat)
		{
			std::string outputFilename = std::string(pngTestList[iFile]) + "__" + FORMATIDNames[iFormat] + ".blp";

			if (!aMemImage.SaveToBLP(outputFilename, BLPType(iFormat)))
				return false;
		}
	}

	// BLP->PNG test.
	for (int iFile = 0; iFile < 4; ++iFile)
	{
		std::string inputFilename = std::string(pngTestList[iFile]) + ".png";
		if (!aMemImage.LoadFromPNG(inputFilename))
			return false;

		for (int iFormat = 1; iFormat < BLPTYPE_COUNT; ++iFormat)
		{
			std::string outputFilename = std::string(pngTestList[iFile]) + "__" + FORMATIDNames[iFormat] + ".blp";

			if (!aMemImage.SaveToBLP(outputFilename, BLPType(iFormat)))
				return false;
		}
	}
	*/

	return true;
}

static int ProcessFile(const char* pszFilenameArgument, const char* pszDestinationFilename)
{
	std::string_view filenameArg(pszFilenameArgument ? pszFilenameArgument : "");
	std::string_view destFilename(pszDestinationFilename ? pszDestinationFilename : "");

	std::string filenameBuffer(filenameArg);
	auto periodPos = filenameBuffer.rfind('.');
	if (periodPos == std::string::npos) {
		std::cout << "Invalid filename '" << filenameArg << "' (no . in it).\n";
		return -1;
	}
	std::string extension = filenameBuffer.substr(periodPos + 1);
	filenameBuffer.erase(periodPos);

	std::string extLower = extension;
	std::transform(extLower.begin(), extLower.end(), extLower.begin(),
		[](unsigned char c) { return static_cast<char>(std::tolower(c)); });

	FileType inputFileType;
	FORMATID inputFormatID;
	MemImage aMemImage;
	if (extLower == "blp") {
		inputFileType = FILETYPE_BLP;
		if (auto result = aMemImage.LoadFromBLP(pszFilenameArgument, &inputFormatID))
			return int(result);
	}
	else if (extLower == "png") {
		inputFileType = FILETYPE_PNG;
		if (!aMemImage.LoadFromPNG(pszFilenameArgument, &inputFormatID))
			return -1;
	}
	else {
		std::cout << "ERROR: Input file '" << filenameArg << "' not a png or blp.\n";
		return -1;
	}

	if (g_bInfoMode)
		return 0;

	FORMATID targetFormatID = g_targetFormatID;
	if (FORMAT_UNSPECIFIED == targetFormatID)
		targetFormatID = MemImage::s_ruleTable[inputFormatID];

	std::string targetFilename;
	if (destFilename.empty()) {
		bool bSameFiletype = (FILETYPE_BLP == inputFileType && ISBLP(targetFormatID)) ||
			(FILETYPE_PNG == inputFileType && ISPNG(targetFormatID));
		targetFilename = filenameBuffer + (bSameFiletype ? "_" : "") + "." + (ISBLP(targetFormatID) ? "blp" : "png");
	}
	else {
		targetFilename = destFilename;
	}

	if (targetFormatID >= 0 && targetFormatID < FORMAT_COUNT) {
		std::cout << "Converting: " << filenameArg << " (" << FORMATIDNames[inputFormatID] << ") -> " << targetFilename << " (" << FORMATIDNames[targetFormatID] << ")\n";
	}
	else {
		std::cout << "ERROR: targetFormatID is out of bounds.\n";
	}

	auto result = aMemImage.Save(targetFilename.c_str(), targetFormatID);
	return result ? 0 : -1;
}

static FORMATID GetFormatFromString(const char *string)
{
	std::string_view str(string ? string : "");
	std::string input(str);
	std::transform(input.begin(), input.end(), input.begin(),
		[](unsigned char c) { return static_cast<char>(std::tolower(c)); });

	for (int iType = 1; iType < FORMAT_COUNT; ++iType)
	{
		std::string name = FORMATIDNames[iType];
		std::transform(name.begin(), name.end(), name.begin(),
			[](unsigned char c) { return static_cast<char>(std::tolower(c)); });

		if (input == name)
			return static_cast<FORMATID>(iType);
	}

	std::cout << "ERROR: \"" << str << "\" not a valid format string.\n";
	return FORMAT_UNSPECIFIED;
}

static void ListFormats()
{
	std::cout << "****************\n"
			  << "* File Formats *\n"
			  << "****************\n"
			  << "Format Name     Description\n"
			  << "________________________________________________\n";
	for (int ii = 1; ii < FORMAT_COUNT; ++ii)
		std::cout << FORMATIDNames[ii] << " \t" << FORMATIDDescriptions[ii] << "\n";

	std::cout << "\n**************************\n"
			  << "* Conversion Rules Table *\n"
			  << "**************************\n"
			  << "Source Format    Target Format\n"
			  << "________________________________________________\n";
	for (int ii = 1; ii < FORMAT_COUNT; ++ii)
		std::cout << FORMATIDNames[ii] << " \t> " << FORMATIDNames[MemImage::s_ruleTable[ii]] << "\n";

	std::cout << "\nThe Conversion Rules table shows the format given to the destination file\n"
			  << "when the source file has a given format. You can change a rule with the /U\n"
			  << "option, or you can force the destination file into a given format with /F.\n"
			  << "/U can be specified multiple times. BLP->BLP and PNG->PNG is OK.\n\n"
			  << "Examples:\n"
			  << "  blpconverter /FPNG_RGB myfile.blp\n"
			  << "  blpconverter /UBLP_PAL_A0=PNG_RGB /UPNG_PAL=PNG_RGB myfile.blp\n";
}

constexpr auto INDENT = "\n           ";

static void Usage()
{
	std::cout << "\nBLPCONVERTER: Converts BLP files to PNGs and vice versa.\n"
			  << "Version 8.4 (C) 2011 Patrick Cyr (<EMAIL>)\n"
			  << "This program is free software under the GNU General Public License.\n\n"
			  << "BLPCONVERTER [options] sourceFile [targetFile | sourceFile [...]]\n\n"
			  << "sourceFile The file to convert.\n"
			  << "targetFile Optionally, the name of the converted file. If omitted, target\n"
			  << "            file is given the same name as sourceFile but with the opposite\n"
			  << "            extension.\n\n"
			  << "/A(value) Sets the Alpha threshold when converting from palettized, 8-bit\n"
			  << "            BLPs to palettized PNGs. Value is a number between 0 and 255.\n"
			  << "            Source alpha values below the threshold are fully transparent, above\n"
			  << "            are fully opaque. Default is " << MemImage::s_byAlphaThreshold << ".\n"
			  << "/C          Create mip test image. Outputs an image which contains all of the\n"
			  << "            generated mip levels.\n"
			  << "/E          Pause on Error. (Handy for drag-and-drop use.)\n"
			  << "/F(format) Forces target Format. Overrides all other settings, including\n"
			  << "            targetFile extension.\n"
			  << "/H          Force WoW cHaracter texture format (palettized, no alpha) when\n"
			  << "            making BLPs.\n"
			  << "/I          Info mode. Only outputs file information. This option\n"
			  << "            automatically sets the /V and /M options.\n"
			  << "/L          Lists formats and conversion rules.\n"
			  << "/M          Multi-file mode. In this mode, multiple files can be input after\n"
			  << "            options. It is not possible to specify custom output names for them\n"
			  << "            in this mode.\n"
			  << "/N          No mips. Disables creation of mip levels when saving BLPs.\n"
			  << "/P          Pause upon completion. (Handy for drag-and-drop use.)\n"
			  << "/R          Force WoW clothing texture formats. All created BLPs are palettized\n"
			  << "            and all PNGs are RGB/RGBA.\n"
			  << "/U(format)=(format)\n"
			  << "            Change conversion rUle. See /L.\n"
			  << "/V          Verbose mode. Outputs additional information.\n";
}

int main(int argc, char *argv[])
{
	int result = 0;

	const char *sourceFilename = nullptr;
	const char *destFilename = nullptr;
	bool bMultiFileMode = false;
	bool bPauseAtEnd = false;
	bool bPauseOnError = false;
	bool bRunTest = false;
	bool bClothingOptionSet = false;
	bool bCharacterOptionSet = false;

	if (argc == 1)
	{
		Usage();
		result = -1;
		goto Finish;
	}

	int iArg;
	for (iArg = 1; iArg < argc; ++iArg)
	{
		if ('-' == argv[iArg][0] || '/' == argv[iArg][0])
		{
			switch (tolower(argv[iArg][1]))
			{
			case '?':
				Usage();
				result = 0;
				goto Finish;
				break;
			case 'a':
			{
				int threshold = atoi(&argv[iArg][2]);
				if (threshold < 0 || threshold > 255)
				{
					std::cout << "ERROR: Alpha threshold must be between 0 and 255.\n";
					result = -1;
					goto Finish;
				}
				MemImage::s_byAlphaThreshold = static_cast<BYTE>(threshold);
				break;
			}
			case 'c':
				MemImage::s_bCreateMipTestImage = true;
				break;
			case 'e':
				bPauseOnError = true;
				break;
			case 'f':
			{
				const char *arg = &argv[iArg][2];
				g_targetFormatID = FORMAT_UNSPECIFIED;

				g_targetFormatID = GetFormatFromString(arg);
				if (FORMAT_UNSPECIFIED == g_targetFormatID)
				{
					ListFormats();
					result = -1;
					goto Finish;
				}

				break;
			}
			/*
			case 'g':
				MemImage::s_fGammaFactor = (float) atof(&argv[iArg][2]);
				if (MemImage::s_fGammaFactor < 0.0f || MemImage::s_fGammaFactor > 1.0f)
				{
					printf("ERROR: Gamma factor must between 0.0 and 1.0.\n");
					result = -1;
					goto Finish;
				}
				break;
			*/
			case 'h':
				bCharacterOptionSet = true;
				MemImage::s_ruleTable[PNGTYPE_PALETTIZED_ALPHAMASK] = BLPTYPE_PAL_ALPHA0;
				MemImage::s_ruleTable[PNGTYPE_RGB] = BLPTYPE_PAL_ALPHA0;
				MemImage::s_ruleTable[PNGTYPE_RGBA] = BLPTYPE_PAL_ALPHA0;
				break;
			case 'i':
				std::cout << "File Info Mode.\n";
				g_bInfoMode = true;
				MemImage::s_bVerbose = true;
				bMultiFileMode = true;
				break;
			case 'l':
				ListFormats();
				goto Finish;
				break;
			case 'm':
				bMultiFileMode = true;
				break;
			case 'n':
				MemImage::s_bNoMips = true;
				break;
			case 'p':
				bPauseAtEnd = true;
				break;
			case 'r':
				bClothingOptionSet = true;
				MemImage::s_ruleTable[PNGTYPE_RGB] = BLPTYPE_PAL_ALPHA0;
				MemImage::s_ruleTable[PNGTYPE_RGBA] = BLPTYPE_PAL_ALPHA8;
				MemImage::s_ruleTable[BLPTYPE_PAL_ALPHA0] = PNGTYPE_RGB;
				MemImage::s_ruleTable[BLPTYPE_PAL_ALPHA1] = PNGTYPE_RGBA;
				MemImage::s_ruleTable[BLPTYPE_PAL_ALPHA4] = PNGTYPE_RGBA;
				MemImage::s_ruleTable[BLPTYPE_PAL_ALPHA8] = PNGTYPE_RGBA;
				break;
			case 't':
				bRunTest = true;
				break;
			case 'u':
			{
				char arg[128];
				::strncpy(arg, &argv[iArg][2], 127);
				arg[127] = 0; // Ensure null termination.
				char *pszGT = ::strchr(arg, '=');
				if (nullptr == pszGT)
				{
					std::cout << "ERROR: No \"=\" character found in /U option.\n";
					result = -1;
					goto Finish;
				}
				pszGT[0] = 0;
				char *arg2 = &pszGT[1];
				FORMATID srcID = GetFormatFromString(arg);
				FORMATID destID = GetFormatFromString(arg2);

				if (FORMAT_UNSPECIFIED == srcID || FORMAT_UNSPECIFIED == destID)
				{
					ListFormats();
					result = -1;
					goto Finish;
				}

				MemImage::s_ruleTable[srcID] = destID;
				break;
			}
			case 'v':
				MemImage::s_bVerbose = true;
				break;
			default:
				std::cout << "ERROR: " << argv[iArg][1] << " is not a valid option.\n";
				result = -1;
				goto Finish;
			}
		}
		else
		{
			break;
		}
	}

	if (bRunTest)
	{
		if (!RunTest())
			result = -1;
		goto Finish;
	}

	if (bClothingOptionSet && bCharacterOptionSet)
	{
		std::cout << "ERROR: -r and -h are exclusive.\n";
		result = -1;
		goto Finish;
	}

	if (iArg == argc)
	{
		std::cout << "ERROR: Filename argument missing.\n";
		result = -1;
		goto Finish;
	}
	else if (bMultiFileMode)
	{
		do
		{
#ifndef _linux_
		std::string filepath = argv[iArg];
		auto slash = filepath.find_last_of("\\/");
		if (slash != std::string::npos)
			filepath.erase(slash + 1);
		else
			filepath.clear();

		WIN32_FIND_DATAA aFindData;
		HANDLE hFindFile = ::FindFirstFileA(argv[iArg], &aFindData);
		if (INVALID_HANDLE_VALUE == hFindFile)
		{
			std::cout << "ERROR: Couldn't find file '" << argv[iArg] << "'.\n";
			result = -1;
		}
		else
		{
			do
			{
				std::string foundFile = filepath + aFindData.cFileName;

				result = ProcessFile(foundFile.c_str(), nullptr);
				if (result != 0)
					break;
			} while (FindNextFileA(hFindFile, &aFindData));

			FindClose(hFindFile);
		}
#else
			DIR *dp;
			struct dirent *de;

			dp = opendir(dirname(argv[iArg]));
			if (dp == nullptr)
			{
				std::cout << "ERROR: Couldn't find file '" << argv[iArg] << "' (" << errno << ").\n";
				result = -1;
			}
			else
			{

				while ((de = readdir(dp)) != nullptr)
				{
					if (fnmatch(basename(argv[iArg]), de->d_name, FNM_PATHNAME) == 0)
					{
						result = ProcessFile(de->d_name, nullptr);
						if (result != 0)
						{
							break;
						}
					}
				}
				closedir(dp);
			}
#endif

			++iArg;
		} while ((result == 0) && (iArg < argc));
	}
	else
	{
		sourceFilename = argv[iArg++];
		destFilename = (iArg < argc && !bMultiFileMode) ? argv[iArg] : "";

		result = ProcessFile(sourceFilename, destFilename);
	}

Finish:
	if (bPauseAtEnd || (bPauseOnError && result != 0))
	{
		std::cout << "Press any key to continue...\n";
#ifndef _linux_
		[[maybe_unused]] int c = _getch();
#else
		getchar();
#endif
	}
	return result;
}