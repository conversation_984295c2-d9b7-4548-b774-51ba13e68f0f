#!/bin/bash

# 配置项
VCXPROJ_FILE="BLPConverter.vcxproj.filters"
HEADER_DIR="../imagequant"
FILTER_NAME="Header Files"
MARKER="<!-- insert here -->"
TMP_FILE="$(mktemp)"

# 构建要插入的内容
generate_clinclude_blocks() {
  for filepath in "$HEADER_DIR"/*.c; do
    filename=$(basename "$filepath")
    cat <<EOF
<ClInclude Include="..\\imagequant\\$filename">
      <Filter>imagequant</Filter>
    </ClInclude>
EOF
  done
}

# 处理插入
insert_blocks() {
  inserted=0
  while IFS= read -r line || [[ -n "$line" ]]; do
    echo "$line"
    if [[ $inserted -eq 0 && "$line" == *"$MARKER"* ]]; then
      generate_clinclude_blocks
      inserted=1
    fi
  done < "$VCXPROJ_FILE" > "$TMP_FILE"

  # 替换原文件
  mv "$TMP_FILE" "$VCXPROJ_FILE"
  echo "✅ 插入完成：已写入 $VCXPROJ_FILE"
}

insert_blocks