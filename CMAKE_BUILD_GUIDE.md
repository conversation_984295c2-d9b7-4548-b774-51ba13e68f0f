# BLPConverter CMake Build Guide

本指南说明如何使用CMake构建BLPConverter项目，该配置基于原有的MSBuild配置创建。

## 前提条件

### Windows
- Visual Studio 2022 (推荐) 或 Visual Studio 2019
- CMake 3.16 或更高版本
- Git (用于获取依赖库源码)

### Linux
- GCC 9+ 或 Clang 10+
- CMake 3.16 或更高版本
- OpenMP 支持 (用于squish库)

## 依赖库配置

CMake配置支持两种方式处理依赖库：

### 方式1：使用外部依赖库 (推荐，匹配MSBuild配置)

1. 下载并解压依赖库到项目目录的上级目录：
   ```
   d:\dev\
   ├── BLPConverter\          # 项目目录
   ├── zlib-1.3.1\           # zlib源码
   ├── lpng1648\             # libpng源码
   └── squish-1.11\          # squish源码
   ```

2. 依赖库下载地址：
   - **zlib**: https://zlib.net/ (下载 zlib-1.3.1.tar.gz)
   - **libpng**: http://www.libpng.org/pub/png/libpng.html (下载 lpng1648.zip)
   - **squish**: https://sourceforge.net/projects/libsquish/ (下载 squish-1.11.tar.gz)

### 方式2：使用本地副本

如果外部依赖库不存在，CMake会自动使用项目目录下的本地副本：
- `zlib/` - zlib本地副本
- `libpng/` - libpng本地副本  
- `squish/` - squish本地副本

## 构建步骤

### 使用CMake预设 (推荐)

项目包含预配置的CMake预设，可以简化构建过程：

```bash
# 配置Debug版本
cmake --preset x64-debug

# 构建Debug版本
cmake --build --preset x64-debug

# 配置Release版本
cmake --preset x64-release

# 构建Release版本
cmake --build --preset x64-release
```

### 手动配置和构建

#### Windows (Visual Studio)

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目 (Debug)
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Debug

# 构建项目
cmake --build . --config Debug

# 或者配置Release版本
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

#### Linux

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目 (Debug)
cmake .. -DCMAKE_BUILD_TYPE=Debug

# 构建项目
make -j$(nproc)

# 或者配置Release版本
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

## 自定义依赖库路径

如果依赖库位于非标准位置，可以通过CMake变量指定：

```bash
cmake .. \
  -DZLIB_SRC_DIR=/path/to/zlib \
  -DPNG_SRC_DIR=/path/to/libpng \
  -DSQUISH_SRC_DIR=/path/to/squish
```

## 构建选项

### 可选功能

- `BUILD_TESTS=ON` - 构建测试程序 (默认: OFF)

```bash
cmake .. -DBUILD_TESTS=ON
```

### 编译器特定选项

CMake配置会自动匹配MSBuild的编译器设置：

**Debug配置**:
- 优化级别: 无优化 (/Od)
- 运行时检查: 启用 (/RTC1)
- OpenMP: 启用 (squish库)
- 调试信息: 完整

**Release配置**:
- 优化级别: 最大速度 (/O2 /Oi)
- 链接时代码生成: 启用 (/GL /LTCG)
- SSE2优化: 启用 (squish库)
- 帧指针省略: 启用 (/Oy)

## 输出文件

构建完成后，输出文件位于：

- **Debug版本**: `build/Debug/BLPConverter[d].exe`
- **Release版本**: `build/Release/BLPConverter.exe`

静态库文件：
- **Debug**: `squishd.lib`, `libpng16d.lib`, `zlibd.lib`
- **Release**: `squish.lib`, `libpng16.lib`, `zlib.lib`

## 安装

```bash
# 安装到默认位置
cmake --install build --config Release

# 安装到指定位置
cmake --install build --config Release --prefix /path/to/install
```

## 故障排除

### 常见问题

1. **找不到依赖库源码**
   - 确保依赖库路径正确
   - 检查CMake输出中的路径信息

2. **OpenMP链接错误**
   - 确保编译器支持OpenMP
   - Linux上可能需要安装 `libomp-dev`

3. **Visual Studio版本不匹配**
   - 更新CMakePresets.json中的生成器版本
   - 或使用 `-G` 参数指定正确的生成器

### 调试信息

CMake配置会输出详细的配置信息，包括：
- 依赖库路径
- 编译器信息
- 构建类型
- 输出目录

检查这些信息可以帮助诊断配置问题。

## 与MSBuild的差异

CMake配置尽可能匹配MSBuild设置，但存在一些差异：

1. **输出目录结构**: CMake使用标准的Debug/Release目录结构
2. **预编译头**: 暂未实现libpng的预编译头支持
3. **资源文件**: 暂未包含Windows资源文件编译

这些差异不影响核心功能，生成的可执行文件具有相同的功能。
