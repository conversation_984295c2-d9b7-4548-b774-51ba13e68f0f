# BLPConverter CMake Build Guide

This guide explains how to build BLPConverter using CMake instead of the original MSBuild system.

## Prerequisites

1. **CMake 3.20 or later**
2. **Visual Studio 2022** (or Visual Studio Build Tools 2022)
3. **External Dependencies** (see below)

## External Dependencies

The project requires the following external libraries. The CMake configuration will automatically detect them if they are placed in the expected locations:

### Required Dependencies

1. **zlib-1.3.1** - Should be located at `../zlib-1.3.1` (relative to project root)
2. **lpng1648** - Should be located at `../lpng1648` (relative to project root)  
3. **squish-1.11** - Should be located at `../squish-1.11` (relative to project root)
4. **imagequant** - Uses precompiled library (`Release/imagequant_sys.lib`)

### Dependency Structure

Your directory structure should look like this:
```
parent_directory/
├── BLPConverter/          # This project
│   ├── Release/           # Contains imagequant_sys.lib
│   └── Debug/             # May contain debug version of imagequant
├── zlib-1.3.1/          # zlib source
├── lpng1648/             # libpng source
└── squish-1.11/          # squish source
```

### Fallback to Local Copies

If external dependencies are not found, CMake will look for local copies in these subdirectories:
- `zlib/` (for zlib)
- `libpng/` (for libpng)
- `squish/` (for squish)

## Quick Start

### Using the Build Script (Recommended)

The easiest way to build the project is using the provided batch script:

```batch
# Build Debug version
build.bat debug

# Build Release version  
build.bat release

# Build both versions
build.bat both

# Clean build directories
build.bat clean

# Test built executables
build.bat test
```

### Manual CMake Commands

If you prefer to use CMake directly:

```batch
# Configure Debug build
cmake --preset x64-debug

# Build Debug version
cmake --build --preset x64-debug

# Configure Release build
cmake --preset x64-release

# Build Release version
cmake --build --preset x64-release
```

### Alternative Manual Method

```batch
# Debug build
cmake -B build/debug -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Debug
cmake --build build/debug --config Debug

# Release build
cmake -B build/release -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release
cmake --build build/release --config Release
```

## Output

### Debug Build
- Executable: `build/x64-debug/Debug/BLPConverterd.exe`
- Libraries: `build/x64-debug/Debug/*.lib`

### Release Build  
- Executable: `build/x64-release/Release/BLPConverter.exe`
- Libraries: `build/x64-release/Release/*.lib`

## Configuration Details

### Compiler Settings

The CMake configuration matches the original MSBuild settings:

**Debug Configuration:**
- Runtime Library: MultiThreadedDebugDLL (/MDd)
- Optimization: Disabled (/Od)
- Debug Information: Full (/Zi)
- Library suffix: 'd' (e.g., zlibd.lib)

**Release Configuration:**
- Runtime Library: MultiThreadedDLL (/MD)
- Optimization: Maximum Speed (/O2)
- SSE2 optimizations enabled for squish library
- Whole Program Optimization (/GL)

### Dependencies

1. **zlib** - Compression library (built from source)
2. **libpng** - PNG image handling (built from source)
3. **squish** - Texture compression with OpenMP support (built from source)
4. **imagequant** - Color quantization (precompiled library)

### Special Features

- **OpenMP Support**: Enabled for squish library for parallel processing
- **SSE2 Optimizations**: Enabled in Release mode for better performance
- **Cross-platform**: While optimized for Windows, the CMake configuration supports Linux builds
- **Precompiled Libraries**: Handles imagequant as a precompiled library

## Troubleshooting

### Common Issues

1. **Dependencies not found**
   - Ensure external dependencies are in the correct relative paths
   - Check that the directory names match exactly (case-sensitive on Linux)
   - Verify imagequant_sys.lib exists in Release/ directory

2. **CMake version too old**
   - Update to CMake 3.20 or later

3. **Visual Studio not found**
   - Install Visual Studio 2022 or Visual Studio Build Tools 2022
   - Ensure the C++ workload is installed

4. **Build errors**
   - Clean the build directory: `build.bat clean`
   - Try rebuilding: `build.bat both`

5. **Linking errors with imagequant**
   - Ensure `Release/imagequant_sys.lib` exists
   - Check that the library was compiled with compatible settings

### Getting Help

If you encounter issues:
1. Check the CMake configuration output for missing dependencies
2. Verify all external dependencies are properly installed
3. Try cleaning and rebuilding the project
4. Check that imagequant library is present and accessible

## Differences from MSBuild

### Advantages of CMake
- Cross-platform support (Windows, Linux, macOS)
- Better dependency management
- Modern build system with presets
- Easier integration with IDEs and CI/CD systems
- Automatic handling of precompiled libraries

### Key Changes
- Uses CMake presets instead of solution configurations
- Automatic dependency detection with fallbacks
- Unified build script for easy usage
- Better handling of external vs local dependencies
- Proper handling of precompiled imagequant library

## Advanced Usage

### Custom Dependency Paths

You can override dependency paths using CMake variables:

```batch
cmake -B build/debug -G "Visual Studio 17 2022" -A x64 ^
  -DZLIB_SRC_DIR="C:/path/to/zlib" ^
  -DPNG_SRC_DIR="C:/path/to/libpng" ^
  -DSQUISH_SRC_DIR="C:/path/to/squish"
```

### Linux Build

For Linux builds (experimental):

```bash
# Configure
cmake --preset linux-debug

# Build  
cmake --build --preset linux-debug
```

Note: Linux builds require installing the dependencies through your package manager or building them separately. The imagequant library may need to be built separately for Linux.

## Build Status

✅ **Successfully Tested Configurations:**
- Windows x64 Debug build
- Windows x64 Release build
- CMake presets working correctly
- All external dependencies detected
- Precompiled imagequant library integrated
- Build script functionality verified
- Duplicate function definition resolved
- Both Debug and Release executables tested and working

## Recent Fixes

- ✅ Fixed duplicate `BuildMipmap` function definition in MemImage.cpp
- ✅ Integrated precompiled imagequant library support
- ✅ Updated CMake presets to use Visual Studio generator
- ✅ Created convenient build.bat script for easy building
- ✅ Verified both Debug and Release builds work correctly
