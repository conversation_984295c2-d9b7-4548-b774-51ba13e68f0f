cmake_minimum_required(VERSION 3.16)
project(BLPConverter LANGUAGES C CXX)

# 设置C++和C标准 (匹配MSBuild配置)
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 17)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 配置输出目录 (匹配MSBuild结构)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release)

# 外部依赖库源码路径 (可通过环境变量或CMake变量覆盖)
# 这些路径对应MSBuild中BLPConverter.props的设置
set(ZLIB_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../zlib-1.3.1" CACHE PATH "Path to zlib source directory")
set(PNG_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../lpng1648" CACHE PATH "Path to libpng source directory")
set(SQUISH_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../squish-1.11" CACHE PATH "Path to squish source directory")

# 检查外部依赖是否存在，如果不存在则使用本地副本
if(NOT EXISTS ${ZLIB_SRC_DIR})
    set(ZLIB_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/zlib")
    message(STATUS "Using local zlib at: ${ZLIB_SRC_DIR}")
else()
    message(STATUS "Using external zlib at: ${ZLIB_SRC_DIR}")
endif()

if(NOT EXISTS ${PNG_SRC_DIR})
    set(PNG_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/libpng")
    message(STATUS "Using local libpng at: ${PNG_SRC_DIR}")
else()
    message(STATUS "Using external libpng at: ${PNG_SRC_DIR}")
endif()

if(NOT EXISTS ${SQUISH_SRC_DIR})
    set(SQUISH_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/squish")
    message(STATUS "Using local squish at: ${SQUISH_SRC_DIR}")
else()
    message(STATUS "Using external squish at: ${SQUISH_SRC_DIR}")
endif()

# 设置依赖库的构建选项，遵循原作者的设计意图
# 禁用不需要的功能以减少构建时间和复杂性
set(ZLIB_BUILD_EXAMPLES OFF CACHE BOOL "Disable zlib examples")
set(PNG_SHARED OFF CACHE BOOL "Disable libpng shared library")
set(PNG_STATIC ON CACHE BOOL "Enable libpng static library")
set(PNG_TESTS OFF CACHE BOOL "Disable libpng tests")
set(PNG_TOOLS OFF CACHE BOOL "Disable libpng tools")
set(PNG_EXECUTABLES OFF CACHE BOOL "Disable libpng executables")
set(PNG_FRAMEWORK OFF CACHE BOOL "Disable libpng framework")
set(PNG_HARDWARE_OPTIMIZATIONS ON CACHE BOOL "Enable libpng hardware optimizations")

# 主程序源文件
set(MAIN_SOURCES
    BLPConverter.cpp
    MemImage.cpp
)

set(MAIN_HEADERS
    BLP.h
    MemImage.h
    libimagequant.h
    port.h
)

# ============================================================================
# ZLIB 依赖配置 - 使用官方CMakeLists.txt
# ============================================================================
if(EXISTS ${ZLIB_SRC_DIR}/CMakeLists.txt)
    # 使用zlib官方的CMakeLists.txt，但只构建静态库
    set(BUILD_SHARED_LIBS_BACKUP ${BUILD_SHARED_LIBS})
    set(BUILD_SHARED_LIBS OFF)

    # 添加zlib子目录，使用其官方构建配置
    add_subdirectory(${ZLIB_SRC_DIR} ${CMAKE_BINARY_DIR}/zlib EXCLUDE_FROM_ALL)

    # 恢复BUILD_SHARED_LIBS设置
    set(BUILD_SHARED_LIBS ${BUILD_SHARED_LIBS_BACKUP})

    # 使用zlib官方目标名称
    if(TARGET zlibstatic)
        set(ZLIB_TARGET zlibstatic)
        # 为了兼容性，创建别名
        add_library(zlib ALIAS zlibstatic)
    elseif(TARGET zlib)
        set(ZLIB_TARGET zlib)
    else()
        message(FATAL_ERROR "zlib target not found after adding subdirectory")
    endif()

    message(STATUS "Using zlib official CMakeLists.txt with target: ${ZLIB_TARGET}")
else()
    message(FATAL_ERROR "zlib CMakeLists.txt not found: ${ZLIB_SRC_DIR}/CMakeLists.txt")
endif()

# ============================================================================
# LIBPNG 依赖配置 - 使用官方CMakeLists.txt
# ============================================================================
if(EXISTS ${PNG_SRC_DIR}/CMakeLists.txt)
    # 设置ZLIB变量供libpng的find_package(ZLIB)使用
    if(TARGET ${ZLIB_TARGET})
        # 获取zlib的属性
        get_target_property(ZLIB_INCLUDE_DIR ${ZLIB_TARGET} INTERFACE_INCLUDE_DIRECTORIES)
        if(NOT ZLIB_INCLUDE_DIR)
            set(ZLIB_INCLUDE_DIR ${ZLIB_SRC_DIR})
        endif()

        # 设置FindZLIB.cmake需要的变量
        set(ZLIB_FOUND TRUE CACHE BOOL "ZLIB found")
        set(ZLIB_INCLUDE_DIRS ${ZLIB_INCLUDE_DIR} CACHE PATH "ZLIB include directories")
        set(ZLIB_LIBRARIES ${ZLIB_TARGET} CACHE STRING "ZLIB libraries")
        set(ZLIB_LIBRARY ${ZLIB_TARGET} CACHE STRING "ZLIB library")
        set(ZLIB_INCLUDE_DIR ${ZLIB_INCLUDE_DIR} CACHE PATH "ZLIB include directory")

        # 创建ZLIB::ZLIB目标供libpng使用
        if(NOT TARGET ZLIB::ZLIB)
            add_library(ZLIB::ZLIB ALIAS ${ZLIB_TARGET})
        endif()

        # 设置ZLIB_ROOT以帮助find_package
        set(ZLIB_ROOT ${ZLIB_SRC_DIR} CACHE PATH "ZLIB root directory")
    endif()

    # 添加libpng子目录，使用其官方构建配置
    add_subdirectory(${PNG_SRC_DIR} ${CMAKE_BINARY_DIR}/libpng EXCLUDE_FROM_ALL)

    # 确定libpng目标名称
    if(TARGET png_static)
        set(PNG_TARGET png_static)
        # 为了兼容性，创建别名
        add_library(libpng16 ALIAS png_static)
    elseif(TARGET png)
        set(PNG_TARGET png)
        add_library(libpng16 ALIAS png)
    else()
        message(FATAL_ERROR "libpng target not found after adding subdirectory")
    endif()

    message(STATUS "Using libpng official CMakeLists.txt with target: ${PNG_TARGET}")
else()
    message(FATAL_ERROR "libpng CMakeLists.txt not found: ${PNG_SRC_DIR}/CMakeLists.txt")
endif()

# ============================================================================
# SQUISH 静态库配置 - 手动构建（squish没有官方CMakeLists.txt）
# ============================================================================
if(EXISTS ${SQUISH_SRC_DIR})
    # 收集squish源文件
    set(SQUISH_SOURCES
        ${SQUISH_SRC_DIR}/alpha.cpp
        ${SQUISH_SRC_DIR}/clusterfit.cpp
        ${SQUISH_SRC_DIR}/colourblock.cpp
        ${SQUISH_SRC_DIR}/colourfit.cpp
        ${SQUISH_SRC_DIR}/colourset.cpp
        ${SQUISH_SRC_DIR}/maths.cpp
        ${SQUISH_SRC_DIR}/rangefit.cpp
        ${SQUISH_SRC_DIR}/singlecolourfit.cpp
        ${SQUISH_SRC_DIR}/squish.cpp
    )

    set(SQUISH_HEADERS
        ${SQUISH_SRC_DIR}/alpha.h
        ${SQUISH_SRC_DIR}/clusterfit.h
        ${SQUISH_SRC_DIR}/colourblock.h
        ${SQUISH_SRC_DIR}/colourfit.h
        ${SQUISH_SRC_DIR}/colourset.h
        ${SQUISH_SRC_DIR}/maths.h
        ${SQUISH_SRC_DIR}/rangefit.h
        ${SQUISH_SRC_DIR}/simd.h
        ${SQUISH_SRC_DIR}/simd_float.h
        ${SQUISH_SRC_DIR}/simd_sse.h
        ${SQUISH_SRC_DIR}/simd_ve.h
        ${SQUISH_SRC_DIR}/singlecolourfit.h
        ${SQUISH_SRC_DIR}/singlecolourlookup.inl
        ${SQUISH_SRC_DIR}/squish.h
    )

    add_library(squish STATIC ${SQUISH_SOURCES} ${SQUISH_HEADERS})
    target_include_directories(squish PUBLIC ${SQUISH_SRC_DIR})
    set(SQUISH_TARGET squish)

    # squish编译选项 (匹配MSBuild配置)
    target_compile_definitions(squish PRIVATE
        $<$<CONFIG:Debug>:WIN32;_WINDOWS;SQUISH_USE_OPENMP;_DEBUG>
        $<$<CONFIG:Release>:WIN32;NDEBUG;_LIB;SQUISH_USE_SSE=2>
    )

    if(MSVC)
        target_compile_options(squish PRIVATE
            $<$<CONFIG:Debug>:/Od /RTC1 /openmp>
            $<$<CONFIG:Release>:/O2 /Oi /GL /openmp /Ot /Oy>
        )
        set_target_properties(squish PROPERTIES
            DEBUG_POSTFIX "d"
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL"
        )
    endif()

    message(STATUS "Using manual squish configuration with target: ${SQUISH_TARGET}")
else()
    message(FATAL_ERROR "squish source directory not found: ${SQUISH_SRC_DIR}")
endif()

# ============================================================================
# IMAGEQUANT 库配置 (预编译库)
# ============================================================================
# 检查预编译的imagequant库
set(IMAGEQUANT_LIB_DEBUG "${CMAKE_CURRENT_SOURCE_DIR}/Debug/imagequant_sys.lib")
set(IMAGEQUANT_LIB_RELEASE "${CMAKE_CURRENT_SOURCE_DIR}/Release/imagequant_sys.lib")

if(EXISTS ${IMAGEQUANT_LIB_RELEASE} OR EXISTS ${IMAGEQUANT_LIB_DEBUG})
    # 创建导入库目标
    add_library(imagequant_sys STATIC IMPORTED)

    # 设置库文件路径
    if(EXISTS ${IMAGEQUANT_LIB_DEBUG})
        set_target_properties(imagequant_sys PROPERTIES
            IMPORTED_LOCATION_DEBUG ${IMAGEQUANT_LIB_DEBUG}
        )
    endif()

    if(EXISTS ${IMAGEQUANT_LIB_RELEASE})
        set_target_properties(imagequant_sys PROPERTIES
            IMPORTED_LOCATION_RELEASE ${IMAGEQUANT_LIB_RELEASE}
            IMPORTED_LOCATION ${IMAGEQUANT_LIB_RELEASE}
        )
    endif()

    # 设置包含目录
    target_include_directories(imagequant_sys INTERFACE ${CMAKE_CURRENT_SOURCE_DIR})

    message(STATUS "Found precompiled imagequant library")
    if(EXISTS ${IMAGEQUANT_LIB_DEBUG})
        message(STATUS "  Debug: ${IMAGEQUANT_LIB_DEBUG}")
    endif()
    if(EXISTS ${IMAGEQUANT_LIB_RELEASE})
        message(STATUS "  Release: ${IMAGEQUANT_LIB_RELEASE}")
    endif()
else()
    message(STATUS "imagequant library not found - some features may be disabled")
endif()

# ============================================================================
# 主程序配置
# ============================================================================
add_executable(BLPConverter ${MAIN_SOURCES} ${MAIN_HEADERS})

# 包含目录设置
target_include_directories(BLPConverter PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${SQUISH_SRC_DIR}
)

# 链接库 - 使用实际的目标名称
target_link_libraries(BLPConverter PRIVATE
    ${SQUISH_TARGET}
    ${PNG_TARGET}
    ${ZLIB_TARGET}
)

# 如果imagequant库存在，则链接它
if(TARGET imagequant_sys)
    target_link_libraries(BLPConverter PRIVATE imagequant_sys)
endif()

# 主程序编译选项 (匹配MSBuild配置)
target_compile_definitions(BLPConverter PRIVATE
    $<$<CONFIG:Debug>:_DEBUG;_CRT_SECURE_NO_WARNINGS>
    $<$<CONFIG:Release>:NDEBUG;_CRT_SECURE_NO_WARNINGS>
)

if(WIN32)
    # Windows特定设置 (匹配MSBuild)
    if(MSVC)
        target_compile_options(BLPConverter PRIVATE
            $<$<CONFIG:Debug>:/W3 /Od /RTC1>
            $<$<CONFIG:Release>:/W4 /O2 /Oi /GL /Ot /Oy>
        )

        # 链接器设置 (匹配MSBuild)
        target_link_options(BLPConverter PRIVATE
            $<$<CONFIG:Release>:/LTCG>
        )

        # 系统库链接 (匹配MSBuild)
        target_link_libraries(BLPConverter PRIVATE
            kernel32 user32 gdi32 winspool comdlg32 advapi32
            shell32 ole32 oleaut32 uuid odbc32 odbccp32
            Ws2_32 Userenv ntdll
        )

        set_target_properties(BLPConverter PROPERTIES
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL"
            DEBUG_POSTFIX "d"
        )
    endif()
else()
    # Linux特定设置
    target_compile_definitions(BLPConverter PRIVATE _linux_)
    target_compile_options(BLPConverter PRIVATE -Wall -Wextra)
    target_link_libraries(BLPConverter PRIVATE m)
endif()

# 设置输出名称和路径
set_target_properties(BLPConverter PROPERTIES
    OUTPUT_NAME "BLPConverter"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release
)

# ============================================================================
# 安装配置
# ============================================================================
install(TARGETS BLPConverter
    RUNTIME DESTINATION bin
    COMPONENT Runtime
)

# 安装依赖库 (可选)
if(WIN32 AND MSVC)
    # 安装实际的目标，而不是别名
    install(TARGETS ${ZLIB_TARGET} ${PNG_TARGET} ${SQUISH_TARGET}
        ARCHIVE DESTINATION lib
        COMPONENT Development
    )

    # 注意：导入的库(IMPORTED)不能使用install(TARGETS)
    # 如果需要安装imagequant库，需要手动复制文件
endif()

# ============================================================================
# 测试配置 (可选)
# ============================================================================
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    enable_testing()
    # 这里可以添加测试程序
endif()

# ============================================================================
# 打包配置 (可选)
# ============================================================================
set(CPACK_PACKAGE_NAME "BLPConverter")
set(CPACK_PACKAGE_VERSION "8.4")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "BLP file format converter")
set(CPACK_PACKAGE_VENDOR "Patrick Cyr")
include(CPack)

# ============================================================================
# 开发者信息输出
# ============================================================================
message(STATUS "")
message(STATUS "BLPConverter CMake Configuration Summary:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  C Standard: ${CMAKE_C_STANDARD}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME} ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "")
message(STATUS "Dependencies:")
message(STATUS "  zlib: ${ZLIB_SRC_DIR} (target: ${ZLIB_TARGET})")
message(STATUS "  libpng: ${PNG_SRC_DIR} (target: ${PNG_TARGET})")
message(STATUS "  squish: ${SQUISH_SRC_DIR} (target: ${SQUISH_TARGET})")
if(TARGET imagequant_sys)
    message(STATUS "  imagequant: precompiled library")
else()
    message(STATUS "  imagequant: NOT FOUND")
endif()
message(STATUS "")
message(STATUS "Output directories:")
message(STATUS "  Debug: ${CMAKE_BINARY_DIR}/Debug")
message(STATUS "  Release: ${CMAKE_BINARY_DIR}/Release")
message(STATUS "")
