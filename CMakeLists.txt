cmake_minimum_required(VERSION 3.16)
project(BLPConverter LANGUAGES C CXX)

# 设置C++和C标准 (匹配MSBuild配置)
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 17)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 配置输出目录 (匹配MSBuild结构)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release)

# 外部依赖库源码路径 (可通过环境变量或CMake变量覆盖)
# 这些路径对应MSBuild中BLPConverter.props的设置
set(ZLIB_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../zlib-1.3.1" CACHE PATH "Path to zlib source directory")
set(PNG_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../lpng1648" CACHE PATH "Path to libpng source directory")
set(SQUISH_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../squish-1.11" CACHE PATH "Path to squish source directory")

# 检查外部依赖是否存在，如果不存在则使用本地副本
if(NOT EXISTS ${ZLIB_SRC_DIR})
    set(ZLIB_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/zlib")
    message(STATUS "Using local zlib at: ${ZLIB_SRC_DIR}")
else()
    message(STATUS "Using external zlib at: ${ZLIB_SRC_DIR}")
endif()

if(NOT EXISTS ${PNG_SRC_DIR})
    set(PNG_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/libpng")
    message(STATUS "Using local libpng at: ${PNG_SRC_DIR}")
else()
    message(STATUS "Using external libpng at: ${PNG_SRC_DIR}")
endif()

if(NOT EXISTS ${SQUISH_SRC_DIR})
    set(SQUISH_SRC_DIR "${CMAKE_CURRENT_SOURCE_DIR}/squish")
    message(STATUS "Using local squish at: ${SQUISH_SRC_DIR}")
else()
    message(STATUS "Using external squish at: ${SQUISH_SRC_DIR}")
endif()

# 主程序源文件
set(MAIN_SOURCES
    BLPConverter.cpp
    MemImage.cpp
)

set(MAIN_HEADERS
    BLP.h
    MemImage.h
    libimagequant.h
    port.h
)

# ============================================================================
# ZLIB 静态库配置
# ============================================================================
if(EXISTS ${ZLIB_SRC_DIR})
    # 收集zlib源文件
    set(ZLIB_SOURCES
        ${ZLIB_SRC_DIR}/adler32.c
        ${ZLIB_SRC_DIR}/compress.c
        ${ZLIB_SRC_DIR}/crc32.c
        ${ZLIB_SRC_DIR}/deflate.c
        ${ZLIB_SRC_DIR}/infback.c
        ${ZLIB_SRC_DIR}/inffast.c
        ${ZLIB_SRC_DIR}/inflate.c
        ${ZLIB_SRC_DIR}/inftrees.c
        ${ZLIB_SRC_DIR}/trees.c
        ${ZLIB_SRC_DIR}/uncompr.c
        ${ZLIB_SRC_DIR}/zutil.c
    )

    add_library(zlib STATIC ${ZLIB_SOURCES})
    target_include_directories(zlib PUBLIC ${ZLIB_SRC_DIR})

    # zlib编译选项 (匹配MSBuild配置)
    target_compile_definitions(zlib PRIVATE
        $<$<CONFIG:Debug>:WIN32;_DEBUG;Z_SOLO>
        $<$<CONFIG:Release>:WIN32;NDEBUG;Z_SOLO>
    )

    if(MSVC)
        target_compile_options(zlib PRIVATE
            $<$<CONFIG:Debug>:/Od /RTC1>
            $<$<CONFIG:Release>:/O2 /Oi /GL>
        )
        set_target_properties(zlib PROPERTIES
            DEBUG_POSTFIX "d"
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL"
        )
    endif()
else()
    message(FATAL_ERROR "zlib source directory not found: ${ZLIB_SRC_DIR}")
endif()

# ============================================================================
# LIBPNG 静态库配置
# ============================================================================
if(EXISTS ${PNG_SRC_DIR})
    # 收集libpng源文件
    set(PNG_SOURCES
        ${PNG_SRC_DIR}/png.c
        ${PNG_SRC_DIR}/pngerror.c
        ${PNG_SRC_DIR}/pngget.c
        ${PNG_SRC_DIR}/pngmem.c
        ${PNG_SRC_DIR}/pngpread.c
        ${PNG_SRC_DIR}/pngread.c
        ${PNG_SRC_DIR}/pngrio.c
        ${PNG_SRC_DIR}/pngrtran.c
        ${PNG_SRC_DIR}/pngrutil.c
        ${PNG_SRC_DIR}/pngset.c
        ${PNG_SRC_DIR}/pngtrans.c
        ${PNG_SRC_DIR}/pngwio.c
        ${PNG_SRC_DIR}/pngwrite.c
        ${PNG_SRC_DIR}/pngwtran.c
        ${PNG_SRC_DIR}/pngwutil.c
    )

    # ARM NEON支持文件 (如果存在)
    if(EXISTS ${PNG_SRC_DIR}/arm)
        list(APPEND PNG_SOURCES
            ${PNG_SRC_DIR}/arm/arm_init.c
            ${PNG_SRC_DIR}/arm/filter_neon_intrinsics.c
            ${PNG_SRC_DIR}/arm/palette_neon_intrinsics.c
        )
    endif()

    add_library(libpng16 STATIC ${PNG_SOURCES})
    target_include_directories(libpng16 PUBLIC ${PNG_SRC_DIR})
    target_include_directories(libpng16 PRIVATE ${ZLIB_SRC_DIR})
    target_link_libraries(libpng16 PRIVATE zlib)

    # libpng编译选项 (匹配MSBuild配置)
    target_compile_definitions(libpng16 PRIVATE
        $<$<CONFIG:Debug>:WIN32;_DEBUG;_CRT_SECURE_NO_WARNINGS>
        $<$<CONFIG:Release>:WIN32;NDEBUG;_CRT_SECURE_NO_WARNINGS>
    )

    if(MSVC)
        target_compile_options(libpng16 PRIVATE
            $<$<CONFIG:Debug>:/Od /RTC1>
            $<$<CONFIG:Release>:/O2 /Oi /GL>
        )
        set_target_properties(libpng16 PROPERTIES
            DEBUG_POSTFIX "d"
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL"
        )
    endif()
else()
    message(FATAL_ERROR "libpng source directory not found: ${PNG_SRC_DIR}")
endif()

# ============================================================================
# SQUISH 静态库配置
# ============================================================================
if(EXISTS ${SQUISH_SRC_DIR})
    # 收集squish源文件
    set(SQUISH_SOURCES
        ${SQUISH_SRC_DIR}/alpha.cpp
        ${SQUISH_SRC_DIR}/clusterfit.cpp
        ${SQUISH_SRC_DIR}/colourblock.cpp
        ${SQUISH_SRC_DIR}/colourfit.cpp
        ${SQUISH_SRC_DIR}/colourset.cpp
        ${SQUISH_SRC_DIR}/maths.cpp
        ${SQUISH_SRC_DIR}/rangefit.cpp
        ${SQUISH_SRC_DIR}/singlecolourfit.cpp
        ${SQUISH_SRC_DIR}/squish.cpp
    )

    set(SQUISH_HEADERS
        ${SQUISH_SRC_DIR}/alpha.h
        ${SQUISH_SRC_DIR}/clusterfit.h
        ${SQUISH_SRC_DIR}/colourblock.h
        ${SQUISH_SRC_DIR}/colourfit.h
        ${SQUISH_SRC_DIR}/colourset.h
        ${SQUISH_SRC_DIR}/maths.h
        ${SQUISH_SRC_DIR}/rangefit.h
        ${SQUISH_SRC_DIR}/simd.h
        ${SQUISH_SRC_DIR}/simd_float.h
        ${SQUISH_SRC_DIR}/simd_sse.h
        ${SQUISH_SRC_DIR}/simd_ve.h
        ${SQUISH_SRC_DIR}/singlecolourfit.h
        ${SQUISH_SRC_DIR}/singlecolourlookup.inl
        ${SQUISH_SRC_DIR}/squish.h
    )

    add_library(squish STATIC ${SQUISH_SOURCES} ${SQUISH_HEADERS})
    target_include_directories(squish PUBLIC ${SQUISH_SRC_DIR})

    # squish编译选项 (匹配MSBuild配置)
    target_compile_definitions(squish PRIVATE
        $<$<CONFIG:Debug>:WIN32;_WINDOWS;SQUISH_USE_OPENMP;_DEBUG>
        $<$<CONFIG:Release>:WIN32;NDEBUG;_LIB;SQUISH_USE_SSE=2>
    )

    if(MSVC)
        target_compile_options(squish PRIVATE
            $<$<CONFIG:Debug>:/Od /RTC1 /openmp>
            $<$<CONFIG:Release>:/O2 /Oi /GL /openmp /Ot /Oy>
        )
        set_target_properties(squish PROPERTIES
            DEBUG_POSTFIX "d"
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL"
        )
    endif()
else()
    message(FATAL_ERROR "squish source directory not found: ${SQUISH_SRC_DIR}")
endif()

# ============================================================================
# IMAGEQUANT 库配置 (预编译库)
# ============================================================================
# 检查预编译的imagequant库
set(IMAGEQUANT_LIB_DEBUG "${CMAKE_CURRENT_SOURCE_DIR}/Debug/imagequant_sys.lib")
set(IMAGEQUANT_LIB_RELEASE "${CMAKE_CURRENT_SOURCE_DIR}/Release/imagequant_sys.lib")

if(EXISTS ${IMAGEQUANT_LIB_RELEASE} OR EXISTS ${IMAGEQUANT_LIB_DEBUG})
    # 创建导入库目标
    add_library(imagequant_sys STATIC IMPORTED)

    # 设置库文件路径
    if(EXISTS ${IMAGEQUANT_LIB_DEBUG})
        set_target_properties(imagequant_sys PROPERTIES
            IMPORTED_LOCATION_DEBUG ${IMAGEQUANT_LIB_DEBUG}
        )
    endif()

    if(EXISTS ${IMAGEQUANT_LIB_RELEASE})
        set_target_properties(imagequant_sys PROPERTIES
            IMPORTED_LOCATION_RELEASE ${IMAGEQUANT_LIB_RELEASE}
            IMPORTED_LOCATION ${IMAGEQUANT_LIB_RELEASE}
        )
    endif()

    # 设置包含目录
    target_include_directories(imagequant_sys INTERFACE ${CMAKE_CURRENT_SOURCE_DIR})

    message(STATUS "Found precompiled imagequant library")
    if(EXISTS ${IMAGEQUANT_LIB_DEBUG})
        message(STATUS "  Debug: ${IMAGEQUANT_LIB_DEBUG}")
    endif()
    if(EXISTS ${IMAGEQUANT_LIB_RELEASE})
        message(STATUS "  Release: ${IMAGEQUANT_LIB_RELEASE}")
    endif()
else()
    message(STATUS "imagequant library not found - some features may be disabled")
endif()

# ============================================================================
# 主程序配置
# ============================================================================
add_executable(BLPConverter ${MAIN_SOURCES} ${MAIN_HEADERS})

# 包含目录设置
target_include_directories(BLPConverter PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${SQUISH_SRC_DIR}
    ${PNG_SRC_DIR}
    ${ZLIB_SRC_DIR}
)

# 链接库
target_link_libraries(BLPConverter PRIVATE
    squish
    libpng16
    zlib
)

# 如果imagequant库存在，则链接它
if(TARGET imagequant_sys)
    target_link_libraries(BLPConverter PRIVATE imagequant_sys)
endif()

# 主程序编译选项 (匹配MSBuild配置)
target_compile_definitions(BLPConverter PRIVATE
    $<$<CONFIG:Debug>:_DEBUG;_CRT_SECURE_NO_WARNINGS>
    $<$<CONFIG:Release>:NDEBUG;_CRT_SECURE_NO_WARNINGS>
)

if(WIN32)
    # Windows特定设置 (匹配MSBuild)
    if(MSVC)
        target_compile_options(BLPConverter PRIVATE
            $<$<CONFIG:Debug>:/W3 /Od /RTC1>
            $<$<CONFIG:Release>:/W4 /O2 /Oi /GL /Ot /Oy>
        )

        # 链接器设置 (匹配MSBuild)
        target_link_options(BLPConverter PRIVATE
            $<$<CONFIG:Release>:/LTCG>
        )

        # 系统库链接 (匹配MSBuild)
        target_link_libraries(BLPConverter PRIVATE
            kernel32 user32 gdi32 winspool comdlg32 advapi32
            shell32 ole32 oleaut32 uuid odbc32 odbccp32
            Ws2_32 Userenv ntdll
        )

        set_target_properties(BLPConverter PROPERTIES
            MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>DLL"
            DEBUG_POSTFIX "d"
        )
    endif()
else()
    # Linux特定设置
    target_compile_definitions(BLPConverter PRIVATE _linux_)
    target_compile_options(BLPConverter PRIVATE -Wall -Wextra)
    target_link_libraries(BLPConverter PRIVATE m)
endif()

# 设置输出名称和路径
set_target_properties(BLPConverter PROPERTIES
    OUTPUT_NAME "BLPConverter"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG ${CMAKE_BINARY_DIR}/Debug
    RUNTIME_OUTPUT_DIRECTORY_RELEASE ${CMAKE_BINARY_DIR}/Release
)

# ============================================================================
# 安装配置
# ============================================================================
install(TARGETS BLPConverter
    RUNTIME DESTINATION bin
    COMPONENT Runtime
)

# 安装依赖库 (可选)
if(WIN32 AND MSVC)
    install(TARGETS zlib libpng16 squish
        ARCHIVE DESTINATION lib
        COMPONENT Development
    )

    # 注意：导入的库(IMPORTED)不能使用install(TARGETS)
    # 如果需要安装imagequant库，需要手动复制文件
endif()

# ============================================================================
# 测试配置 (可选)
# ============================================================================
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    enable_testing()
    # 这里可以添加测试程序
endif()

# ============================================================================
# 打包配置 (可选)
# ============================================================================
set(CPACK_PACKAGE_NAME "BLPConverter")
set(CPACK_PACKAGE_VERSION "8.4")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "BLP file format converter")
set(CPACK_PACKAGE_VENDOR "Patrick Cyr")
include(CPack)

# ============================================================================
# 开发者信息输出
# ============================================================================
message(STATUS "")
message(STATUS "BLPConverter CMake Configuration Summary:")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  C Standard: ${CMAKE_C_STANDARD}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "  Platform: ${CMAKE_SYSTEM_NAME} ${CMAKE_SYSTEM_PROCESSOR}")
message(STATUS "")
message(STATUS "Dependencies:")
message(STATUS "  zlib: ${ZLIB_SRC_DIR}")
message(STATUS "  libpng: ${PNG_SRC_DIR}")
message(STATUS "  squish: ${SQUISH_SRC_DIR}")
if(TARGET imagequant_sys)
    message(STATUS "  imagequant: ${IMAGEQUANT_DIR}")
else()
    message(STATUS "  imagequant: NOT FOUND")
endif()
message(STATUS "")
message(STATUS "Output directories:")
message(STATUS "  Debug: ${CMAKE_BINARY_DIR}/Debug")
message(STATUS "  Release: ${CMAKE_BINARY_DIR}/Release")
message(STATUS "")
