{"version": 3, "configurePresets": [{"name": "windows-base", "hidden": true, "generator": "Visual Studio 17 2022", "architecture": "x64", "binaryDir": "${sourceDir}/build/${presetName}", "installDir": "${sourceDir}/install/${presetName}", "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "x64-debug", "displayName": "x64 Debug", "inherits": "windows-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "x64-release", "displayName": "x64 Release", "inherits": "windows-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}, {"name": "linux-base", "hidden": true, "generator": "Unix Makefiles", "binaryDir": "${sourceDir}/build/${presetName}", "installDir": "${sourceDir}/install/${presetName}", "cacheVariables": {"CMAKE_C_COMPILER": "gcc", "CMAKE_CXX_COMPILER": "g++"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Linux"}}, {"name": "linux-debug", "displayName": "Linux Debug", "inherits": "linux-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "linux-release", "displayName": "Linux Release", "inherits": "linux-base", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "x64-debug", "displayName": "Windows x64 Debug", "configurePreset": "x64-debug", "configuration": "Debug"}, {"name": "x64-release", "displayName": "Windows x64 Release", "configurePreset": "x64-release", "configuration": "Release"}, {"name": "linux-debug", "displayName": "Linux Debug", "configurePreset": "linux-debug"}, {"name": "linux-release", "displayName": "Linux Release", "configurePreset": "linux-release"}]}