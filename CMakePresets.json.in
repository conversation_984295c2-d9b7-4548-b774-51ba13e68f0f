{"version": 3, "configurePresets": [{"name": "windows-base", "hidden": true, "generator": "Ninja", "binaryDir": "${sourceDir}/build/${presetName}", "installDir": "${sourceDir}/install/${presetName}", "cacheVariables": {"CMAKE_C_COMPILER": "cl.exe", "CMAKE_CXX_COMPILER": "cl.exe"}, "condition": {"type": "equals", "lhs": "${hostSystemName}", "rhs": "Windows"}}, {"name": "x64-debug", "displayName": "x64 Debug", "inherits": "windows-base", "architecture": {"value": "x64", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}}, {"name": "x64-release", "displayName": "x64 Release", "inherits": "windows-base", "architecture": {"value": "x64", "strategy": "external"}, "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}}], "buildPresets": [{"name": "x64-debug", "configurePreset": "x64-debug"}, {"name": "x64-release", "configurePreset": "x64-release"}]}