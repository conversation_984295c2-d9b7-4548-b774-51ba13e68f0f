/* MemImage.cpp : (C) 2008 <PERSON> Cy<PERSON>

Implements MemImage class.  There are 4 types of MemImages, palettized/RGB & alpha/no-alpha.
MemImage has built-in functions to convert between any of it's types.  MemImage also
implements Save/Load functions for various file types.  Currently, just PNG and BLP.
Because all types of PNG and BLP load into the same 4 types of MemImages, you can convert
any of the supported file types into any of the others.

TODO: Break the specific loading and saving functions out and keep them in arrays to
make it easier to add support for other file types.
*/

// #define SQUISH_USE_SSE 1

#ifdef _linux_
#include "port.h"
#include <libgen.h>
#include <sys/types.h>
#include <dirent.h>
#include <fnmatch.h>
#include <errno.h>
#endif

#include <list>
#include <vector>
#include <png.h>
#include "MemImage.h"
#include "squish.h"
#include "BLP.h"
#include "libimagequant.h"

const char *FORMATIDNames[] = {
    "[UNSPECIFIED]",
    "BLP_PAL_A0",
    "BLP_PAL_A1",
    "BLP_PAL_A4",
    "BLP_PAL_A8",
    "BLP_DXT1_A0",
    "BLP_DXT1_A1",
    "BLP_DXT3",
    "BLP_DXT5",
    "BLP_BGRA",
    "PNG_PAL",
    "PNG_PAL_MASK",
    "PNG_RGB",
    "PNG_RGBA",
};

const char *FORMATIDDescriptions[] = {
    "[INVALID]",
    "Palettized (no alpha)",
    "Palettized (1-bit alpha)",
    "Palettized (4-bit alpha)",
    "Palettized (full alpha)",
    "DXT1 (no alpha)",
    "DXT1 (alpha)",
    "DXT3",
    "DXT5",
    "BGRA",
    "Palettized (no alpha)",
    "Palettized (with transparency)",
    "RGB",
    "RGBA",
};

FORMATID MemImage::s_ruleTable[FORMAT_COUNT] =
    {
        FORMAT_UNSPECIFIED,

        PNGTYPE_PALETTIZED,           // < BLPTYPE_PAL_ALPHA0,
        PNGTYPE_PALETTIZED_ALPHAMASK, // < BLPTYPE_PAL_ALPHA1,
        PNGTYPE_RGBA,                 // < BLPTYPE_PAL_ALPHA4,
        PNGTYPE_RGBA,                 // < BLPTYPE_PAL_ALPHA8,
        PNGTYPE_RGB,                  // < BLPTYPE_DXT1_ALPHA0,
        PNGTYPE_RGBA,                 // < BLPTYPE_DXT1_ALPHA1,
        PNGTYPE_RGBA,                 // < BLPTYPE_DXT3
        PNGTYPE_RGBA,                 // < BLPTYPE_DXT5,
        PNGTYPE_RGBA,                 // < BLPTYPE_BGRA

        BLPTYPE_PAL_ALPHA0,  // < PNGTYPE_PALETTIZED,
        BLPTYPE_PAL_ALPHA1,  // < PNGTYPE_PALETTIZED_ALPHAMASK,
        BLPTYPE_DXT1_ALPHA0, // < PNGTYPE_RGB,
        BLPTYPE_DXT3,        // < PNGTYPE_RGBA,
};

///////////////////////////////////////////////////////////////////////////////
// Mip Stuff
// http://number-none.com/product/Mipmapping,%20Part%201/index.html

constexpr auto RO = 0;
constexpr auto GO = 1;
constexpr auto BO = 2;

const BYTE SO[4][2] =
{
    {0, 0},
    {0, 1},
    {1, 0},
    {1, 1},
};

///////////////////////////////////////////////////////////////////////////////
// Palette sorting code.

// struct HSB
// {
//     BYTE index;
//     DWORD h, s, b;

//     bool operator<(HSB &rhs)
//     {
//         if (h != rhs.h)
//             return h < rhs.h;
//         if (s != rhs.s)
//             return s < rhs.s;
//         return b < rhs.b;
//     }
// };

// static HSB RGBToHSB(int r, int g, int b)
// {
//     HSB hsb{};

//     int BMax = r > g ? (r > b ? r : b) : (g > b ? g : b);
//     int BMin = r < g ? (r < b ? r : b) : (g < b ? g : b);
//     float diff = float(BMax - BMin);

//     // Brightness: max component
//     hsb.b = (DWORD)((BMax / 255.0f) * 100 + 0.5f);

//     // Saturation
//     hsb.s = (DWORD)(BMax != 0 ? (diff / BMax) * 100 + 0.5f : 0);

//     // Hue
//     if (diff == 0.0f)
//     {
//         hsb.h = 0;
//     }
//     else
//     {
//         float hue;
//         if (r == BMax)
//             hue = (g - b) / diff;
//         else if (g == BMax)
//             hue = 2.0f + (b - r) / diff;
//         else // b == BMax
//             hue = 4.0f + (r - g) / diff;

//         hue *= 60.0f;
//         if (hue < 0.0f)
//             hue += 360.0f;

//         hsb.h = DWORD(hue + 0.5f);
//     }

//     return hsb;
// }

// static void SortPalette(BYTE *palette, DWORD numEntries)
// {
//     typedef std::list<HSB> HSBList;
//     HSBList hsbList;

//     HSB hsb;
//     DWORD ii;
//     for (ii = 0; ii < numEntries; ++ii)
//     {
//         hsb = RGBToHSB(palette[ii * 3 + 0], palette[ii * 3 + 1], palette[ii * 3 + 2]);
//         hsb.index = static_cast<BYTE>(ii);

//         hsbList.insert(hsbList.end(), hsb);
//     }

//     hsbList.sort();

//     BYTE tempPalette[MEMIMAGE_PALETTEBYTES]{};
//     // Copy the sorted palette back into the tempPalette.
//     HSBList::iterator it;
//     for (it = hsbList.begin(), ii = 0; it != hsbList.end(); ++it, ++ii)
//     {
//         HSB *pHSB = &(*it);
//         int ix = pHSB->index;
//         tempPalette[ii * 3 + 0] = palette[ix * 3 + 0];
//         tempPalette[ii * 3 + 1] = palette[ix * 3 + 1];
//         tempPalette[ii * 3 + 2] = palette[ix * 3 + 2];
//     }
//     memcpy(palette, tempPalette, numEntries * 3);
// }

///////////////////////////////////////////////////////////////////////////////
// BitArray struct--used to simplify access to the 1-bit alpha array.

struct BitArray
{
    BYTE *m_buffer;
    DWORD m_bytes;
    DWORD m_length;

    BitArray();
    ~BitArray();
    void New();
    void SetLength(DWORD length);
    // Returns either 0x00 or 0x01, or 0xFF if error
    BYTE Get(DWORD index) const;
    // bit needs to be either 0x00 or 0x01.
    bool Set(DWORD index, BYTE bit);
};

BitArray::BitArray()
{
    m_buffer = nullptr;
    New();
}

BitArray::~BitArray()
{
    New();
}

void BitArray::New()
{
    m_bytes = 0;
    m_length = 0;
    if (m_buffer == nullptr)
		return;
    delete[] m_buffer;
    m_buffer = nullptr;
    
}

void BitArray::SetLength(DWORD length)
{
    New();
    m_bytes = length / 8 + (length % 8 == 0 ? 0 : 1);
    m_length = length;
    m_buffer = new BYTE[m_bytes];
    std::memset(m_buffer, 0, m_bytes);
}

BYTE BitArray::Get(DWORD index) const
{
    if (index >= m_length)
        return 0xFF;

    return m_buffer[index / 8] & (0x1 << index % 8);
}

// bit needs to be either 0x00 or 0x01.
bool BitArray::Set(DWORD index, BYTE bit)
{
    if (index >= m_length)
        return false;
    if (0x00 != bit && 0x01 != bit)
        return false;

    BYTE &hostByte = m_buffer[index / 8];
    BYTE mask = (bit << index % 8);
    if (bit)
        hostByte = m_buffer[index / 8] | mask;
    else if (hostByte & mask)
        hostByte -= mask;
    // else both are already zero.

    return true;
}

///////////////////////////////////////////////////////////////////////////////

// This class is used for SAVING BLP files.
class BLPMemFile
{
public:
    BLPHeader aHeader;
    BYTE aPalette[4 * 256];
    BYTE* pMips[16];

public:
    BLPMemFile()
    {
        memcpy(aHeader.id, BLPID, 4);
        aHeader.version = 1;
        for (int mipIndex = 0; mipIndex < 16; ++mipIndex)
        {
            aHeader.mipSizes[mipIndex] = 0;
            aHeader.mipOffsets[mipIndex] = 0;
            pMips[mipIndex] = nullptr;

        }
        
        std::memset(&aPalette, 0, sizeof(BYTE) * 4 * 256);
    }

    ~BLPMemFile()
    {
        Clear();
    }

    void Clear() {
        for (int mipIndex = 0; mipIndex < 16; ++mipIndex) {
            delete[] pMips[mipIndex];
            pMips[mipIndex] = nullptr;
        }  
    }

    void SetPalette(const png_color *pPaletteEntries)
    {
        int ii;
        for (ii = 0; ii < 256; ++ii)
        {
            // BLP palettes are BGRA
            aPalette[ii * 4 + 2] = pPaletteEntries[ii].red;
            aPalette[ii * 4 + 1] = pPaletteEntries[ii].green;
            aPalette[ii * 4 + 0] = pPaletteEntries[ii].blue;
            aPalette[ii * 4 + 3] = static_cast<BYTE>(0); // guessing.  Seems to have no affect.
        };
    }

    bool Save(const char* pszFilename) const
    {
        // Calculate the mip offsets.
        int mipIndex = 0;
        /*DWORD offset = sizeof(BLPHeader) + (sizeof(BYTE) * 4 * 256);
        int mipCount = 0;
        for (mipIndex = 0; mipIndex < 16; ++mipIndex)
        {
            if (aHeader.mipSizes[mipIndex] > 0)
            {
                ++mipCount;
                aHeader.mipOffsets[mipIndex] = offset;
                offset += aHeader.mipSizes[mipIndex];
            }
            else
            {
                break;
            }
        }*/

        // Open the file for writing.
        FILE *hOutputFile = std::fopen(pszFilename, "wb");
        if (nullptr == hOutputFile)
        {
            std::cout << "ERROR: Couldn't open " << pszFilename << "for writing.";
            return false;
        }

        // Write the header.
        std::fwrite(&aHeader, sizeof(BLPHeader), 1, hOutputFile);

        // Write the palette.
        std::fwrite(&aPalette, sizeof(BYTE) * 4, 256, hOutputFile);

        // Output each mip level.
        for (mipIndex = 0; mipIndex < 16; ++mipIndex)
        {
            if (aHeader.mipSizes[mipIndex] > 0)
            {
                std::fwrite(pMips[mipIndex], sizeof(BYTE), aHeader.mipSizes[mipIndex], hOutputFile);
            }
        }

        /////////////////

        // Close file.
        std::fclose(hOutputFile);
        hOutputFile = nullptr;

        return true;
    }
};

///////////////////////////////////////////////////////////////////////////////
// MemImage class

// Public settings.
bool MemImage::s_bCreateMipTestImage = false;
float MemImage::s_fGammaFactor = 1.0;
BYTE MemImage::s_byAlphaThreshold = 0x80;
bool MemImage::s_bVerbose = false;
bool MemImage::s_bNoMips = false;
bool MemImage::s_bOnlyWarnOnPatch = false;

MemImage::MemImage()
{
    m_buffer = nullptr;
    m_bufferBytes = 0;
    Clear();
}

MemImage::~MemImage()
{
    Clear();
}

MemImage::MemImage(const MemImage &rhs)
{
    m_buffer = nullptr;
    m_bufferBytes = 0;
    Clear();
    Copy(rhs);
}

MemImage &MemImage::operator=(const MemImage &rhs)
{
    Clear();
    Copy(rhs);

    return *this;
}

void MemImage::Copy(const MemImage &rhs)
{
    m_bHasAlpha = rhs.m_bHasAlpha;
    m_bPalettized = rhs.m_bPalettized;
    m_width = rhs.m_width;
    m_height = rhs.m_height;
    std::memcpy(m_palette, rhs.m_palette, MEMIMAGE_PALETTEBYTES);
    m_buffer = new BYTE[rhs.m_bufferBytes];
    m_bufferBytes = rhs.m_bufferBytes;
    std::memcpy(m_buffer, rhs.m_buffer, rhs.m_bufferBytes);
}

void MemImage::Clear()
{
    m_bPalettized = false;
    m_bHasAlpha = false;
    m_width = 0;
    m_height = 0;
    if (m_buffer == nullptr)
        return;
    delete[] m_buffer;
    m_buffer = nullptr;
    m_bufferBytes = 0;
}

DWORD MemImage::CalculateBufferBytes(DWORD width, DWORD height, bool bHasAlpha, bool bPalettized)
{
    DWORD pixelCount = width * height;
    DWORD bpp = 1;
    if (bPalettized)
    {
        if (bHasAlpha)
            bpp = 2;
    }
    else
    {
        if (bHasAlpha)
            bpp = 4;
        else
            bpp = 3;
    }
    return pixelCount * bpp;
}

bool MemImage::AllocateBuffer(size_t bytes)
{
    if (0 == bytes)
        bytes = CalculateBufferBytes(m_width, m_height, m_bHasAlpha, m_bPalettized);

    if (m_buffer)
    {
        delete[] m_buffer;
        m_buffer = nullptr;
        m_bufferBytes = 0;
    }
    m_buffer = new BYTE[bytes];
    if (nullptr == m_buffer)
    {
        std::cout << "ERROR: Failed to allocate buffer (" << bytes << " bytes).\n";
        return false;
    }
    m_bufferBytes = bytes;

    return true;
}

LoadResult MemImage::LoadFromBLP(const char *filename, FORMATID *blpTypeRet)
{
    Clear();

    FILE *fileInput = std::fopen(filename, "rb");
    if (nullptr == fileInput)
    {
        errno_t err;
        _get_errno(&err);
        std::cerr << "ERROR opening " << filename << ": " << err << std::endl;
        return LOADRESULT_FILEERROR;
    }
    if (s_bVerbose)
        std::cout << filename << ":\n";

    // Load the entire file into a buffer.
    std::fseek(fileInput, 0, SEEK_END);
    DWORD dwFileBytes = std::ftell(fileInput);
    BYTE *fileBuffer = new BYTE[dwFileBytes];
    std::fseek(fileInput, 0, SEEK_SET);
    std::fread(fileBuffer, dwFileBytes, 1, fileInput);
    std::fclose(fileInput);

    // File size sanity check.
    if (dwFileBytes < sizeof(BLPHeader))
    {
        std::cout << "ERROR: File size (" << dwFileBytes << ") is smaller than the size of a header, not a known type of BLP.\n";
        return LOADRESULT_NOTBLP;
    }

    BLPHeader *pHeader = (BLPHeader *)fileBuffer;
    BYTE *pPalette = (BYTE *)&(fileBuffer[sizeof(BLPHeader)]);

    // Check Header 4-character id.
    char id[5];
    std::strncpy(id, pHeader->id, 4);
    id[4] = 0;
    if (0 != std::strcmp("BLP2", id))
    {
        if (0 == std::strcmp("PTCH", id))
        {
            std::cout << "WARNING: This is a Patch (PTCH) file, not an actual BLP (BLP2).\n";
            return LOADRESULT_PATCH;
        }

        std::cout << "ERROR: Unexpected ID '" << id << "'--not a BLP file?\n";
        return LOADRESULT_NOTBLP;
    }

    // Check Version
    if (1 != pHeader->version)
    {
        std::cout << "ERROR: Unsupported BLP version " << pHeader->version << ".\n";

        return LOADRESULT_VERSION;
    }

    /////////////////////
    m_width = pHeader->xResolution;
    m_height = pHeader->yResolution;
    DWORD pixelCount = m_width * m_height;

    if (s_bVerbose)
    {
        std::cout << "\t" << pHeader->xResolution << "x" << pHeader->yResolution << "\n";
        std::cout << "\tencoding = " << static_cast<int>(pHeader->encoding) << " (" << (pHeader->encoding < BLP_ENCODING_COUNT ? encodingStrings[pHeader->encoding] : "UNRECOGNIZED") << ")\n";
        std::cout << "\talphaBitDepth = " << static_cast<int>(pHeader->alphaBitDepth) << "\n";
        std::cout << "\talphaEncoding = " << static_cast<int>(pHeader->alphaEncoding) << "\n";

        if (pHeader->hasMips)
        {
            int mipCount = 0;
            while (0 != pHeader->mipOffsets[mipCount])
                ++mipCount;
            std::cout << "\t" << mipCount << " mip levels.\n";
        }
        else
            std::cout << "\tno mips\n";
    }

    // Determine the BLP type.
    FORMATID blpType = FORMAT_UNSPECIFIED;
    if (BLP_ENCODING_PALETTIZED == pHeader->encoding)
    {
        m_bPalettized = true;
        m_bHasAlpha = true;

        // Determine alpha type.
        DWORD bpp = 2;
        switch (pHeader->alphaBitDepth)
        {
        case 0:
            blpType = BLPTYPE_PAL_ALPHA0;
            m_bHasAlpha = false;
            bpp = 1;
            break;
        case 1:
            blpType = BLPTYPE_PAL_ALPHA1;
            break;
        case 4:
            blpType = BLPTYPE_PAL_ALPHA4;
            break;
        case 8:
            blpType = BLPTYPE_PAL_ALPHA8;
            break;
        default:
            std::cout << "ERROR: BLP 'alphaBitDepth' field an unrecognized value (" << static_cast<int>(pHeader->alphaBitDepth) << ").\n";
            break;
        }

        // Save the palette.
        // Note that our palette is RGB, but BLP palettes are BGRx.
        for (int ii = 0; ii < 256; ++ii)
        {
            m_palette[ii * 3 + 0] = pPalette[ii * 4 + 2];
            m_palette[ii * 3 + 1] = pPalette[ii * 4 + 1];
            m_palette[ii * 3 + 2] = pPalette[ii * 4 + 0];
        }

        // Save the image data.
        if (!AllocateBuffer(m_width * m_height * bpp)) {
            return LOADRESULT_MEMORY;
        }

        // Save image data.
        BYTE *pImageData = &(fileBuffer[pHeader->mipOffsets[0]]);
        switch (blpType)
        {
        case BLPTYPE_PAL_ALPHA0:
        case BLPTYPE_PAL_ALPHA8:
        {
            std::memcpy(m_buffer, pImageData, m_bufferBytes);
            break;
        }
        case BLPTYPE_PAL_ALPHA1:
        {
            std::memcpy(m_buffer, pImageData, pixelCount);

            // Convert the 1-bit alpha channel to 8-bit.
            BYTE *pAlphaData = &(pImageData[pixelCount]);
            for (DWORD ii = 0; ii < pixelCount; ++ii)
            {
                m_buffer[pixelCount + ii] = (pAlphaData[ii / 8] & (0x1 << (ii % 8))) ? 0xFF : 0;
            }

            break;
        }
        case BLPTYPE_PAL_ALPHA4:
        {
            std::memcpy(m_buffer, pImageData, pixelCount);

            // Convert the 4-bit alpha channel to 8-bit.
            BYTE *pAlphaData = &(pImageData[pixelCount]);
            for (DWORD ii = 0; ii < pixelCount; ++ii)
            {
                // Two alpha values per byte.
                // Note: This could obviously be made faster but I wanted it to be clear as possible.
                BYTE by = pAlphaData[ii / 2];                                  // The byte
                BYTE mask = 0xF << (ii % 2) * 4;                               // The mask to get which of the two we want.
                BYTE maskedBy = by & mask;                                     // The masked byte.
                DWORD shift = (ii % 2) * 4;                                    // The amount of shift needed.
                BYTE alpha4 = maskedBy >> shift;                               // The 4-bit alpha value.
                BYTE alpha = BYTE(float(0xFF) * (float(alpha4) / float(0xF))); // Translated into 8-bit.

                m_buffer[pixelCount + ii] = alpha;
            }

            break;
        }
        }
    }
    else if (BLP_ENCODING_COMPRESSED == pHeader->encoding)
    {
        m_bPalettized = false;
        int squishFlags = squish::kDxt1;

        m_bHasAlpha = true;
        switch (pHeader->alphaBitDepth)
        {
        case 0:
            blpType = BLPTYPE_DXT1_ALPHA0;
            m_bHasAlpha = false;
            break;
        case 1:
            blpType = BLPTYPE_DXT1_ALPHA1;
            break;
        case 4:
        {
            switch (pHeader->alphaEncoding)
            {
            // NOTE: There is no known way to make DXT3 data be "4 bit" instead of "8 bit".
            // Presumably it is just an anomaly of some textures that they have
            // this set.
            case 1:
                blpType = BLPTYPE_DXT3;
                squishFlags = squish::kDxt3;
                break;
            default:
                std::cout << "ERROR: BLP 'alphaEncoding' field an unrecognized value (" << static_cast<int>(pHeader->alphaEncoding) << ").\n";
                break;
            }
            break;
        }
        case 8:
        {
            switch (pHeader->alphaEncoding)
            {
            case 1:
                blpType = BLPTYPE_DXT3;
                squishFlags = squish::kDxt3;
                break;
            case 7:
                blpType = BLPTYPE_DXT5;
                squishFlags = squish::kDxt5;
                break;
            default:
                std::cout << "ERROR: BLP 'alphaEncoding' field an unrecognized value (" << static_cast<int>(pHeader->alphaEncoding) << ").\n";
                break;
            }
            break;
        }
        case 72:
        {
            // Only seeing this on doodads.
            std::cout << "WARNING: alphaBitDepth = 72, which is recognized but not well understood. Treating as DXT5.\n";
            switch (pHeader->alphaEncoding)
            {
            case 7:
            default:
                blpType = BLPTYPE_DXT5;
                squishFlags = squish::kDxt5;
                break;
            }
            break;
        }
        default:
            std::cout << "ERROR: BLP 'alphaBitDepth' field an unrecognized value (" << static_cast<int>(pHeader->alphaBitDepth) << ").\n";
            break;
        }

        ////////////

        void *Source = &(fileBuffer[pHeader->mipOffsets[0]]);
        squish::u8 *Dest = new squish::u8[m_width * m_height * 4];

        // Do the conversion.
        squish::DecompressImage(
            Dest,
            m_width,
            m_height,
            Source,
            squishFlags
        );

        // Create a buffer for the data.
        DWORD bpp = (0 == pHeader->alphaBitDepth) ? 3 : 4;
        if (!AllocateBuffer(m_width * m_height * bpp))
            return LOADRESULT_MEMORY;

        // Copy into our own buffer.
        if (0 == pHeader->alphaBitDepth)
        {
            for (DWORD ii = 0; ii < pixelCount; ++ii)
            {
                std::memcpy(&m_buffer[ii * 3], &Dest[ii * 4], 3);
            }
        }
        else
        {
            std::memcpy(m_buffer, Dest, m_width * m_height * bpp);
        }

        // Cleanup.
        delete[] Dest;
        Dest = nullptr;
    }
    else if (BLP_ENCODING_BGRA == pHeader->encoding)
    {
        blpType = BLPTYPE_BGRA;
        m_bPalettized = false;
        m_bHasAlpha = true;

        if (8 != pHeader->alphaBitDepth)
        {
            std::cout << "WARNING: alphaBitDepth is " << static_cast<int>(pHeader->alphaBitDepth) << ", expected 8.  Will treat data as though it was 8.\n";
        }

        if (m_width * m_height * 4 != pHeader->mipSizes[0])
        {
            std::cout << "ERROR: mip0 size unexpected.";
            return LOADRESULT_ERROR;
        }

        if (!AllocateBuffer(m_width * m_height * 4))
            return LOADRESULT_MEMORY;

        BYTE *source = &(fileBuffer[pHeader->mipOffsets[0]]);
        for (DWORD ii = 0; ii < m_width * m_height; ++ii)
        {
            m_buffer[ii * 4 + 0] = source[ii * 4 + 2];
            m_buffer[ii * 4 + 1] = source[ii * 4 + 1];
            m_buffer[ii * 4 + 2] = source[ii * 4 + 0];
            m_buffer[ii * 4 + 3] = source[ii * 4 + 3];
        }
    }

    if (FORMAT_UNSPECIFIED == blpType)
    {
        std::cout << "ERROR: Unexpected blp format.\n";
        return LOADRESULT_ERROR;
    }

    if (blpTypeRet)
        *blpTypeRet = blpType;
    if (s_bVerbose)
        std::cout << "\tFormat = " << FORMATIDNames[blpType] << "(" << FORMATIDDescriptions[blpType] << ").\n";

    // Cleanup.
    delete[] fileBuffer;

    return LOADRESULT_SUCCESS;
}

bool MemImage::LoadFromPNG(const char *filename, FORMATID *pngTypeRet)
{
    bool retVal = true;

    // Clear any existing content.
    Clear();

    // Open the PNG.
    png_structp png_ptr;
    png_infop info_ptr;
    DWORD sig_read = 0;
    FILE *fp;
    if ((fp = std::fopen(filename, "rb")) == nullptr)
    {
        errno_t err;
        _get_errno(&err);
        std::cerr << "ERROR opening " << filename << ": " << err << std::endl;
        return false;
    }
    if (s_bVerbose)
        std::cout << filename << ":\n";

    /* Create and initialize the png_struct with the desired error handler
     * functions.  If you want to use the default stderr and longjump method,
     * you can supply nullptr for the last three parameters.  We also supply the
     * the compiler header file version, so that we know if the application
     * was compiled with a compatible version of the library.  REQUIRED
     */
    png_ptr = png_create_read_struct(PNG_LIBPNG_VER_STRING, nullptr, nullptr, nullptr);
    if (png_ptr == nullptr)
    {
        fclose(fp);
        return false;
    }

    /* Allocate/initialize the memory for image information.  REQUIRED. */
    info_ptr = png_create_info_struct(png_ptr);
    if (info_ptr == nullptr)
    {
        fclose(fp);
        png_destroy_read_struct(&png_ptr, nullptr, nullptr);
        return false;
    }

/* Set error handling if you are using the setjmp/longjmp method (this is
 * the normal method of doing things with libpng).  REQUIRED unless you
 * set up your own error handlers in the png_create_read_struct() earlier.
 */
#pragma warning(push)
#pragma warning(disable : 4611)
    if (setjmp(png_jmpbuf(png_ptr)))
    {
        /* Free all of the memory associated with the png_ptr and info_ptr */
        png_destroy_read_struct(&png_ptr, &info_ptr, nullptr);
        fclose(fp);
        /* If we get here, we had a problem reading the file */
        return false;
    }
#pragma warning(pop)
    /* Set up the input control if you are using standard C streams */
    png_init_io(png_ptr, fp);

    /* If we have already read some of the signature */
    png_set_sig_bytes(png_ptr, sig_read);

    /*
     * If you have enough memory to read in the entire image at once,
     * and you need to specify only transforms that can be controlled
     * with one of the PNG_TRANSFORM_* bits (this presently excludes
     * dithering, filling, setting background, and doing gamma
     * adjustment), then you can read the entire image (including
     * pixels) into the info structure with this call:
     */
    png_read_png(png_ptr, info_ptr, PNG_TRANSFORM_IDENTITY, nullptr);

    /* At this point you have read the entire image */

    ///////////////////////////////////////////////////////////////////////////

    m_width = png_get_image_width(png_ptr, info_ptr);
    m_height = png_get_image_height(png_ptr, info_ptr);
    DWORD pixelCount = m_width * m_height;

    png_byte color_type = png_get_color_type(png_ptr, info_ptr);
    png_size_t rowbytes = png_get_rowbytes(png_ptr, info_ptr);
    png_bytepp row_pointers = png_get_rows(png_ptr, info_ptr);

    int num_trans = 0;
    png_bytep trans_alpha = nullptr;
    png_color_16p trans_color = nullptr;

    png_get_tRNS(png_ptr, info_ptr, &trans_alpha, &num_trans, &trans_color);

    if (s_bVerbose)
        std::cout << "\t" << m_width << "x" << m_height << "\n";

    // Create a buffer for the image data.
    size_t imageBytes = rowbytes * m_height * (num_trans == 0 ? 1 : 2);
    if (!AllocateBuffer(imageBytes))
        return false;

    // Copy the data in row by row.
    if (color_type == PNG_COLOR_TYPE_RGB_ALPHA ||
        color_type == PNG_COLOR_TYPE_RGB ||
        (color_type == PNG_COLOR_TYPE_PALETTE && num_trans == 0))
    {
        for (DWORD row = 0; row < m_height; ++row)
        {
            std::memcpy(&m_buffer[row * rowbytes], row_pointers[row], rowbytes);
        }
    }
    else if (color_type == PNG_COLOR_TYPE_PALETTE && num_trans > 0)
    {
        for (DWORD row = 0; row < m_height; ++row)
        {
            for (DWORD ii = 0; ii < m_width; ++ii)
            {
                BYTE palIx = row_pointers[row][ii];
                m_buffer[row * rowbytes + ii] = palIx;

                BYTE alpha = 255;
                if (palIx < num_trans)
                    alpha = trans_alpha[palIx];
                m_buffer[pixelCount + row * rowbytes + ii] = alpha;
            }
        }
    }
    else
    {
        std::cout << "ERROR: PNG format unsupported.  Format = " << color_type << ",";
        return false;
    }

    // Copy the palette.
    png_colorp palette = nullptr;
    int num_palette = 0;

    png_get_PLTE(png_ptr, info_ptr, &palette, &num_palette);

    if (palette)
    {
        std::memcpy(m_palette, palette, num_palette * sizeof(png_color));
    }

    ///////////////////////////////////////////////////////////////////////////
    // Print image info.

    FORMATID type = FORMAT_UNSPECIFIED;
    m_bPalettized = false;
    m_bHasAlpha = false;
    if (PNG_COLOR_TYPE_PALETTE == color_type)
    {
        m_bPalettized = true;

        if (s_bVerbose)
            std::cout << "\t" << num_palette << "palette entries\n";

        if (num_trans == 0)
        {
            type = PNGTYPE_PALETTIZED;
        }
        else
        {
            type = PNGTYPE_PALETTIZED_ALPHAMASK;
            if (s_bVerbose)
                std::cout << "\t" << num_trans << "transparency values\n";
            m_bHasAlpha = true;
        }
    }
    else if (PNG_COLOR_TYPE_RGB_ALPHA == color_type)
    {
        type = PNGTYPE_RGBA;
        m_bHasAlpha = true;
    }
    else if (PNG_COLOR_TYPE_RGB == color_type)
    {
        type = PNGTYPE_RGB;
    }
    else
    {
        std::cout << "ERROR: Unsupported PNG format.\n";
        retVal = false;
    }
    if (pngTypeRet)
        *pngTypeRet = type;
    if (s_bVerbose)
        std::cout << "\tFormat = " << FORMATIDNames[type] << " (" << FORMATIDDescriptions[type] << ").\n";

    ///////////////////////////////////////////////////////////////////////////

    /* clean up after the read, and free any memory allocated - REQUIRED */
    png_destroy_read_struct(&png_ptr, &info_ptr, nullptr);

    /* close the file */
    fclose(fp);

    /* that's it */
    return retVal;
}

bool MemImage::Save(const char *filename, FORMATID type) const
{
    if (BLPTYPE_FIRST <= type && type <= BLPTYPE_LAST)
        return SaveToBLP(filename, type);
    else if (PNGTYPE_FIRST <= type && type <= PNGTYPE_LAST)
        return SaveToPNG(filename, type);

    std::cout << "ERROR: Save called with invalid type " << type << ".\n";
    return false;
}

bool MemImage::SaveToPNG(const char *filename, FORMATID type) const
{
    // See if we need to make any conversions.
    bool bPalettize = false;
    bool bDepalettize = false;
    bool bRemoveAlpha = false;
    bool bAddAlpha = false;

    if (PNGTYPE_PALETTIZED <= type && type <= PNGTYPE_PALETTIZED_ALPHAMASK)
    {
        if (!m_bPalettized)
            bPalettize = true;
    }
    else
    {
        if (m_bPalettized)
            bDepalettize = true;
    }

    if (PNGTYPE_PALETTIZED == type || PNGTYPE_RGB == type)
    {
        if (m_bHasAlpha)
            bRemoveAlpha = true;
    }
    else
    {
        if (!m_bHasAlpha)
            bAddAlpha = true;
    }

    if (bPalettize || bDepalettize || bRemoveAlpha || bAddAlpha)
    {
        // Create a image to convert.
        // NOTE: The point of doing this is to make the code which actually saves the BLP
        // data simpler.  Since we do this, it can assume that when it is asked to save
        // to a given format the MemImage will be in the nearest corresponding format.
        // For example, if the code below is saving to pal 1-bit alpha, the MemImage will
        // always be palettized w/ alpha.
        MemImage convertedImage(*this);
        if (bPalettize)
        {
            if (!convertedImage.Palettize())
                return false;
        }
        else if (bDepalettize)
        {
            convertedImage.Depalettize();
        }

        if (bRemoveAlpha)
        {
            if (!convertedImage.RemoveAlpha())
                return false;
        }
        else if (bAddAlpha)
        {
            if (!convertedImage.AddAlpha())
                return false;
        }

        return convertedImage.SaveToPNG(filename, type);
    }

    //////////////////
    // PNG initialization:

    // Allocate the png structures.
    png_structp png_ptr = png_create_write_struct(PNG_LIBPNG_VER_STRING, nullptr, nullptr, nullptr);
    if (!png_ptr)
        return false;

    png_infop info_ptr = png_create_info_struct(png_ptr);
    if (!info_ptr)
    {
        png_destroy_write_struct(&png_ptr, (png_infopp) nullptr);
        return false;
    }

/* Set error handling if you are using the setjmp/longjmp method (this is
 * the normal method of doing things with libpng).  REQUIRED unless you
 * set up your own error handlers in the png_create_read_struct() earlier.
 */
#pragma warning(push)
#pragma warning(disable : 4611)
    if (setjmp(png_jmpbuf(png_ptr)))
    {
        /* Free all of the memory associated with the png_ptr and info_ptr */
        png_destroy_read_struct(&png_ptr, &info_ptr, nullptr);
        /* If we get here, we had a problem reading the file */
        return false;
    }
#pragma warning(pop)
    /* Set up the input control if you are using standard C streams */
    FILE *fp = std::fopen(filename, "wb");
    if (!fp)
    {
        std::cout << "ERROR: Couldn't open " << filename << " for writing.\n";
        return false;
    }
    png_init_io(png_ptr, fp);

    // Allocate row pointers.
    BYTE **pRowPointers = new BYTE *[m_height];

    bool bDeleteRows = false;
    int transform = PNG_TRANSFORM_IDENTITY;

    ///////////////////////////////////
    BYTE *tempBuffer = nullptr;
    DWORD pixelCount = m_width * m_height;

    switch (type)
    {
    case PNGTYPE_PALETTIZED:
    case PNGTYPE_PALETTIZED_ALPHAMASK:
    {
        // Create the PNG palette and set it.
        png_color pngPalette[256]{};
        for (int ii = 0; ii < 256; ++ii)
        {
            pngPalette[ii].red = m_palette[ii * 3 + 0];
            pngPalette[ii].green = m_palette[ii * 3 + 1];
            pngPalette[ii].blue = m_palette[ii * 3 + 2];
        }

        // Set the image type.
        png_set_IHDR(png_ptr, info_ptr,
                     m_width,
                     m_height,
                     8, // Bit depth.
                     PNG_COLOR_TYPE_PALETTE,
                     PNG_INTERLACE_NONE,
                     PNG_COMPRESSION_TYPE_DEFAULT,
                     PNG_FILTER_TYPE_DEFAULT);

        // PC: What if type is alpha mask but we have none?  I'm guessing its irrelevant.
        if ((PNGTYPE_PALETTIZED_ALPHAMASK == type) && m_bHasAlpha)
        {

            // We need to juggle around the palette indexes to do the trans-alpha thing, so
            // make a temp copy of the image.
            tempBuffer = new BYTE[pixelCount];
            std::memcpy(tempBuffer, m_buffer, pixelCount);

            // Assign row pointers.
            for (WORD row = 0; row < m_height; ++row)
            {
                pRowPointers[row] = &tempBuffer[row * m_width];
            }

            // Scan the image.  The palette entry of the first pixel with an alpha below a threshold
            // is chosen to be the 0 alpha entry.  The actual 0 entry is moved into the found palette entry.
            // All pixels that are below that alpha are set to the new palette entry 0.

            // Not necessarily lossy if the source was 1-bit alpha, too.
            std::cout << "\t1-bit Alpha\n";

            // This needs to come first, its the way the file format is defined.
            const BYTE ALPHAPALETTEINDEX = 0;

            bool transFound = false;
            bool transIsZeroAlready = false;
            BYTE transparentAlphaIndex;
            BYTE *alphaBuffer = &m_buffer[pixelCount];
            for (DWORD ii = 0; ii < pixelCount; ++ii)
            {
                BYTE alpha = alphaBuffer[ii];

                if (alpha < s_byAlphaThreshold)
                {
                    if (!transFound)
                    {
                        transparentAlphaIndex = tempBuffer[ii];

                        // Dont use 0--thats the one we want.
                        // I'm not really 100% sure about this, but I don't care a whole lot
                        // because this is a lossy conversion.  RGBA is a much better option.
                        if (transparentAlphaIndex != 0)
                        {
                            // We want palette entry 0 to be our transparent one, so swap out
                            // this color with 0 in the palette.^
                            pngPalette[transparentAlphaIndex].red = pngPalette[ALPHAPALETTEINDEX].red;
                            pngPalette[transparentAlphaIndex].green = pngPalette[ALPHAPALETTEINDEX].green;
                            pngPalette[transparentAlphaIndex].blue = pngPalette[ALPHAPALETTEINDEX].blue;

                            // Set to an annoying color for testing.
                            pngPalette[ALPHAPALETTEINDEX].red = 0;
                            pngPalette[ALPHAPALETTEINDEX].green = 0xFF;
                            pngPalette[ALPHAPALETTEINDEX].blue = 0;

                            transFound = true;
                            break;
                        }
                        else
                        {
                            // Never tested case where this is true but transFound is false.
                            transIsZeroAlready = true;
                        }
                    }
                }
            }

            if (transFound)
            {
                for (DWORD ii = 0; ii < pixelCount; ++ii)
                {
                    // If this pixel was an original pallete 0, change it to the entry that 0 was moved to.
                    if (tempBuffer[ii] == ALPHAPALETTEINDEX)
                        tempBuffer[ii] = transparentAlphaIndex;

                    // If this pixel is below the alpha threshold, set it to the 0 alpha palette entry (0).
                    BYTE alpha = alphaBuffer[ii];
                    if (alpha < s_byAlphaThreshold)
                        tempBuffer[ii] = ALPHAPALETTEINDEX;
                }
            }

            if (transFound || transIsZeroAlready)
            {
                png_byte trans[1]{};
                // The [0] means that "alpha for pallete entry 0", and the = 0 means "is zero".
                trans[0] = 0;
                png_set_tRNS(png_ptr, info_ptr, trans, 1, nullptr);

                // Note: I don't understand why, but the resulting image doesn't have the
                // green zero entry created.  It looks correct, but instead of the alpha entry
                // being 0 it is 255.
            }
        }
        else // no alpha
        {
            // Assign row pointers.
            for (WORD row = 0; row < m_height; ++row)
            {
                pRowPointers[row] = &m_buffer[row * m_width];
            }
        }

        // Set the palette.
        png_set_PLTE(png_ptr, info_ptr, pngPalette, 256);

        break;
    }

    case PNGTYPE_RGB:
    case PNGTYPE_RGBA:
    {
        // Set the image type.
        png_set_IHDR(
            png_ptr, 
            info_ptr,
            m_width,
            m_height,
            8, // Bit depth.
            (PNGTYPE_RGBA == type) ? PNG_COLOR_TYPE_RGBA : PNG_COLOR_TYPE_RGB,
            PNG_INTERLACE_NONE,
            PNG_COMPRESSION_TYPE_DEFAULT,
            PNG_FILTER_TYPE_DEFAULT
        );

        // Assign row pointers.
        if (PNGTYPE_RGB == type && m_bHasAlpha)
        {
            // Create a RGB buffer.
            tempBuffer = new BYTE[3 * pixelCount];
            for (DWORD ii = 0; ii < pixelCount; ++ii)
            {
                std::memcpy(&tempBuffer[ii * 3], &m_buffer[ii * 4], 3);
            }

            for (WORD row = 0; row < m_height; ++row)
            {
                pRowPointers[row] = &tempBuffer[row * m_width * 3];
            }
        }
        else if (PNGTYPE_RGBA == type && !m_bHasAlpha)
        {
            // Create a RGBA buffer.
            tempBuffer = new BYTE[4 * pixelCount];
            for (DWORD ii = 0; ii < pixelCount; ++ii)
            {
                std::memcpy(&tempBuffer[ii * 4], &m_buffer[ii * 3], 3);
                tempBuffer[ii * 4 + 3] = 0xFF;
            }

            for (WORD row = 0; row < m_height; ++row)
            {
                pRowPointers[row] = &tempBuffer[row * m_width * 4];
            }
        }
        else
        {
            DWORD bpp = (PNGTYPE_RGBA == type) ? 4 : 3;
            for (WORD row = 0; row < m_height; ++row)
            {
                pRowPointers[row] = &m_buffer[row * m_width * bpp];
            }
        }

        break;
    }

    default:
    {
        std::cout << "ERROR: Unexpected PNG type.\n";
        return false;
        break;
    }
    }

    // Put our image data into the PNG.
    png_set_rows(png_ptr, info_ptr, pRowPointers);

    // *** Do the write.
    png_write_png(png_ptr, info_ptr, transform, nullptr);

    // Clean up.
    if (bDeleteRows)
    {
        for (DWORD ii = 0; ii < m_height; ++ii)
            delete[] pRowPointers[ii];
    }
    delete[] pRowPointers;
    delete[] tempBuffer;

    std::cout << "...done!\n";

    return true;
}

bool MemImage::SaveToBLP(const char *filename, FORMATID type) const
{
    // Determine the destination format.
    // See if we need to make any conversions
    FORMATID destFormat = type;
    bool bPalettize = false;
    bool bDepalettize = false;
    bool bRemoveAlpha = false;
    bool bAddAlpha = false;

    if (BLPTYPE_PAL_ALPHA0 <= destFormat && destFormat <= BLPTYPE_PAL_ALPHA8)
    {
        if (!m_bPalettized)
            bPalettize = true;
    }
    else
    {
        if (m_bPalettized)
            bDepalettize = true;
    }

    if (BLPTYPE_PAL_ALPHA0 == destFormat || BLPTYPE_DXT1_ALPHA0 == destFormat)
    {
        if (m_bHasAlpha)
            bRemoveAlpha = true;
    }
    else
    {
        if (!m_bHasAlpha)
            bAddAlpha = true;
    }

    if (bPalettize || bDepalettize || bRemoveAlpha || bAddAlpha)
    {
        // Create a image to convert.
        // NOTE: The point of doing this is to make the code which actually saves the BLP
        // data simpler.  Since we do this, it can assume that when it is asked to save
        // to a given format the MemImage will be in the nearest corresponding format.
        // For example, if the code below is saving to pal 1-bit alpha, the MemImage will
        // always be palettized w/ alpha.
        MemImage convertedImage(*this);
        if (bPalettize)
        {
            if (!convertedImage.Palettize())
                return false;
        }
        else if (bDepalettize)
        {
            convertedImage.Depalettize();
        }

        if (bRemoveAlpha)
        {
            if (!convertedImage.RemoveAlpha())
                return false;
        }
        else if (bAddAlpha)
        {
            if (!convertedImage.AddAlpha())
                return false;
        }

        return convertedImage.SaveToBLP(filename, destFormat);
    }
    ///////////////////////

    // Create structures to hold the BLP data.
    BLPMemFile aBLPFile;

    // Initialize its header.
    aBLPFile.aHeader.xResolution = m_width;
    aBLPFile.aHeader.yResolution = m_height;
    aBLPFile.aHeader.hasMips = s_bNoMips ? 0 : 1;

    switch (destFormat)
    {
    case BLPTYPE_PAL_ALPHA0:
    {
        aBLPFile.aHeader.alphaBitDepth = 0; // No alpha
        aBLPFile.aHeader.alphaEncoding = 8; // 8 value taken from example
        aBLPFile.aHeader.encoding = BLP_ENCODING_PALETTIZED;
        break;
    }
    case BLPTYPE_PAL_ALPHA1:
    {
        aBLPFile.aHeader.alphaBitDepth = 1; // 1-bit alpha
        aBLPFile.aHeader.alphaEncoding = 1; // 1 value taken from example
        aBLPFile.aHeader.encoding = BLP_ENCODING_PALETTIZED;
        break;
    }
    case BLPTYPE_PAL_ALPHA4:
    {
        aBLPFile.aHeader.alphaBitDepth = 4; // 1-bit alpha
        aBLPFile.aHeader.alphaEncoding = 8; // 8 value taken from example
        aBLPFile.aHeader.encoding = BLP_ENCODING_PALETTIZED;
        break;
    }
    case BLPTYPE_PAL_ALPHA8:
    {
        aBLPFile.aHeader.alphaBitDepth = 8; // Regular alpha
        aBLPFile.aHeader.alphaEncoding = 8; // 8 value taken from example
        aBLPFile.aHeader.encoding = BLP_ENCODING_PALETTIZED;
        break;
    }
    case BLPTYPE_DXT1_ALPHA0:
    {
        aBLPFile.aHeader.alphaBitDepth = 0; // No alpha
        aBLPFile.aHeader.alphaEncoding = 0; // 1 = DXT1
        aBLPFile.aHeader.encoding = BLP_ENCODING_COMPRESSED;
        break;
    }
    case BLPTYPE_DXT1_ALPHA1:
    {
        aBLPFile.aHeader.alphaBitDepth = 1; // No alpha
        aBLPFile.aHeader.alphaEncoding = 0; // 0 = DXT1
        aBLPFile.aHeader.encoding = BLP_ENCODING_COMPRESSED;
        break;
    }
    case BLPTYPE_DXT3:
    {
        aBLPFile.aHeader.alphaBitDepth = 8; //
        aBLPFile.aHeader.alphaEncoding = 1; // 1 = DXT3
        aBLPFile.aHeader.encoding = BLP_ENCODING_COMPRESSED;
        break;
    }
    case BLPTYPE_DXT5:
    {
        aBLPFile.aHeader.alphaBitDepth = 8; //
        aBLPFile.aHeader.alphaEncoding = 7; // 7 = DXT5
        aBLPFile.aHeader.encoding = BLP_ENCODING_COMPRESSED;
        break;
    }
    case BLPTYPE_BGRA:
    {
        aBLPFile.aHeader.alphaBitDepth = 8;
        aBLPFile.aHeader.alphaEncoding = 8; // Totally guessing here.  I don't think it has any meaning.
        aBLPFile.aHeader.encoding = BLP_ENCODING_BGRA;
        break;
    }
    default:
    {
        std::cout << "ERROR: Currently unable to save BLPs with format " << destFormat << ".\n";
        return false;
    }
    }

    ///////////////////////
    // MIP Generation:
    // [0] represents the original image.
    int mipInfoCount = 1;
    MemImage mips[16];
    mips[0] = *this;

    if (!s_bNoMips)
    {
        // Generate each level of mip using the format of the file itself.
        do
        {
            mips[mipInfoCount].BuildMipmap(mips[mipInfoCount - 1]);
            ++mipInfoCount;
        } while (mips[mipInfoCount - 1].m_width > 1 || mips[mipInfoCount - 1].m_height > 1);

        if (s_bCreateMipTestImage)
        {
            // png_color* palette = m_bPalettized ? (png_color*) m_palette : nullptr;
            MemImage debugImage;
            debugImage.SaveMipDebugImage(filename, mips, mipInfoCount);
        }
    }

    DWORD offset = sizeof(BLPHeader) + (sizeof(BYTE) * 4 * 256);
    // Go through each mip level and do the conversion to the target format.
    // This might seem kind of backwards but we have to do it this way for the DXT textures.
    for (int iLevel = 0; iLevel < mipInfoCount; ++iLevel)
    {
        DWORD mipWidth = mips[iLevel].m_width;
        DWORD mipHeight = mips[iLevel].m_height;
        DWORD mipPixelCount = mipWidth * mipHeight;
        BYTE *srcMipBuffer = mips[iLevel].m_buffer;

        // Figure out how big the converted mip level needs to be.
        DWORD convertedMipBytes;
        switch (destFormat)
        {
        case BLPTYPE_PAL_ALPHA0:
        {
            convertedMipBytes = mipWidth * mipHeight;
            break;
        }
        case BLPTYPE_PAL_ALPHA1:
        {
            convertedMipBytes = (mipWidth * mipHeight);
            convertedMipBytes += (convertedMipBytes / 8) + (convertedMipBytes % 8 ? 1 : 0);
            break;
        }
        case BLPTYPE_PAL_ALPHA4:
        {
            convertedMipBytes = (mipWidth * mipHeight);
            convertedMipBytes += (convertedMipBytes / 2); // Images are always a power of 2 so a modulo like above would always be false.
            break;
        }
        case BLPTYPE_PAL_ALPHA8:
        {
            convertedMipBytes = mipWidth * mipHeight * 2;
            break;
        }
        case BLPTYPE_DXT1_ALPHA0:
        {
            int blksize = 8; // 16 for dxt3
            int dx = (mipWidth + 3) >> 2;
            int dy = (mipHeight + 3) >> 2;
            convertedMipBytes = dx * dy * blksize;
            break;
        }
        case BLPTYPE_DXT1_ALPHA1:
        {
            int blksize = 8; // 16 for dxt3
            int dx = (mipWidth + 3) >> 2;
            int dy = (mipHeight + 3) >> 2;
            convertedMipBytes = dx * dy * blksize;
            break;
        }
        case BLPTYPE_DXT3:
        {
            int blksize = 16; // 16 for dxt3
            int dx = (mipWidth + 3) >> 2;
            int dy = (mipHeight + 3) >> 2;
            convertedMipBytes = dx * dy * blksize;
            break;
        }
        case BLPTYPE_DXT5:
        {
            int blksize = 16; // 16 for dxt5 (i think)
            int dx = (mipWidth + 3) >> 2;
            int dy = (mipHeight + 3) >> 2;
            convertedMipBytes = dx * dy * blksize;
            break;
        }
        case BLPTYPE_BGRA:
        {
            convertedMipBytes = mipWidth * mipHeight * 4;
            break;
        }
        default:
        {
            std::cout << "ERROR: Destination format not supported, internal error.\n";
            return false;
        }
        }

        // Allocate a buffer for the converted mip level.
        aBLPFile.aHeader.mipOffsets[iLevel] = offset;
		offset += convertedMipBytes;
        aBLPFile.aHeader.mipSizes[iLevel] = convertedMipBytes;
        aBLPFile.pMips[iLevel] = new BYTE[convertedMipBytes];

        // Do the conversion.
        if (BLPTYPE_PAL_ALPHA0 == destFormat)
        {
            // Copy the data into the file buffer directly.
            std::memcpy(aBLPFile.pMips[iLevel], srcMipBuffer, convertedMipBytes);
        }
        else if (BLPTYPE_PAL_ALPHA1 == destFormat)
        {
            // Copy the indexd data into the file buffer directly.
            std::memcpy(aBLPFile.pMips[iLevel], srcMipBuffer, mipPixelCount);

            // Create the alpha section.
            BitArray alphaBits;
            alphaBits.SetLength(mipPixelCount);
            BYTE *pMipAlphaBuffer = &srcMipBuffer[mipPixelCount];
            for (DWORD iMipPixel = 0; iMipPixel < mipPixelCount; ++iMipPixel)
            {
                alphaBits.Set(iMipPixel, pMipAlphaBuffer[iMipPixel] >= s_byAlphaThreshold ? 0x1 : 0x0);
            }

            BYTE *pAlphaBuffer = &aBLPFile.pMips[iLevel][mipPixelCount];
            std::memcpy(pAlphaBuffer, alphaBits.m_buffer, alphaBits.m_bytes);
        }
        else if (BLPTYPE_PAL_ALPHA4 == destFormat)
        {
            // Copy the indexd data into the file buffer directly.
            std::memcpy(aBLPFile.pMips[iLevel], srcMipBuffer, mipPixelCount);

            DWORD alphaOffset = mipPixelCount;

            // Copy the alpha data into the file buffer directly.
            for (DWORD pixelIx = 0; pixelIx < mipPixelCount - 1; pixelIx += 2)
            {
                // Get alpha values.
                BYTE pixelAlpha0 = BYTE(float(srcMipBuffer[alphaOffset + pixelIx]) * s_fGammaFactor);
                BYTE pixelAlpha1 = BYTE(float(srcMipBuffer[alphaOffset + pixelIx + 1]) * s_fGammaFactor);

                // Convert to 4-bit.
                pixelAlpha0 = pixelAlpha0 >> 4;
                pixelAlpha1 = pixelAlpha1 >> 4;

                // Pack into same byte.
                aBLPFile.pMips[iLevel][alphaOffset + pixelIx / 2] = pixelAlpha0 + (pixelAlpha1 << 4);
            }
        }
        else if (BLPTYPE_PAL_ALPHA8 == destFormat)
        {
            DWORD alphaOffset = convertedMipBytes / 2;

            // Copy the data into the file buffer directly.
            for (DWORD pixelIx = 0; pixelIx < mipWidth * mipHeight; ++pixelIx)
            {
                aBLPFile.pMips[iLevel][pixelIx] = srcMipBuffer[pixelIx];

                BYTE pixelAlpha = srcMipBuffer[alphaOffset + pixelIx];
                aBLPFile.pMips[iLevel][alphaOffset + pixelIx] = BYTE(float(pixelAlpha) * s_fGammaFactor);
            }
        }
        else if (BLPTYPE_DXT1_ALPHA0 <= destFormat && destFormat <= BLPTYPE_DXT5)
        {
            // Setup the source texture
            squish::u8 *Src;
            if (BLPTYPE_DXT1_ALPHA0 == destFormat)
            {
                // squish::CompressImage expects an rgba buffer.
                Src = new squish::u8[mipPixelCount * 4];
                for (DWORD iPixel = 0; iPixel < mipPixelCount; ++iPixel)
                {
                    Src[iPixel * 4 + 0] = srcMipBuffer[iPixel * 3 + 0];
                    Src[iPixel * 4 + 1] = srcMipBuffer[iPixel * 3 + 1];
                    Src[iPixel * 4 + 2] = srcMipBuffer[iPixel * 3 + 2];
                    Src[iPixel * 4 + 3] = 0xFF;
                }
            }
            else
            {
                Src = srcMipBuffer;
            }

            // Set up the destination texture.
            squish::u8 *Dest;
            Dest = new squish::u8[convertedMipBytes];

            int dxtType = squish::kDxt1;
            if (BLPTYPE_DXT3 == destFormat)
                dxtType = squish::kDxt3;
            else if (BLPTYPE_DXT5 == destFormat)
                dxtType = squish::kDxt5;

            // Do the conversion.
            squish::CompressImage(Src, mipWidth, mipHeight, Dest, dxtType);

            // Copy into the BLP structure.
            std::memcpy(aBLPFile.pMips[iLevel], Dest, convertedMipBytes);

            delete[] Dest;

            if (BLPTYPE_DXT1_ALPHA0 == destFormat)
                delete[] Src;
        }
        else if (BLPTYPE_BGRA == destFormat)
        {
            // Copy the data into the file buffer, swapping B and R.
            for (DWORD iPixel = 0; iPixel < mipPixelCount; ++iPixel)
            {
                aBLPFile.pMips[iLevel][iPixel * 4 + 0] = srcMipBuffer[iPixel * 4 + 2];
                aBLPFile.pMips[iLevel][iPixel * 4 + 1] = srcMipBuffer[iPixel * 4 + 1];
                aBLPFile.pMips[iLevel][iPixel * 4 + 2] = srcMipBuffer[iPixel * 4 + 0];
                aBLPFile.pMips[iLevel][iPixel * 4 + 3] = srcMipBuffer[iPixel * 4 + 3];
            }
        }
        else
        {
            std::cout << "ERROR: Internal error.\n";
            return false;
        }

        mips[iLevel].Clear();
    }

    // Set the palette.
    if (m_bPalettized)
    {
        aBLPFile.SetPalette((png_color *)m_palette);
    }

    // Create a new blp.
    if (!aBLPFile.Save(filename))
        return false;

    std::cout << "...done!\n";

    return true;
}

bool MemImage::Palettize()
{
    if (m_bPalettized)
        return true;

    DWORD pixelCount = m_width * m_height;
    BYTE bpp = m_bHasAlpha ? 4 : 3;

    std::vector<BYTE> rgba;
    rgba.reserve(pixelCount * 4);
    for (DWORD i = 0; i < pixelCount; ++i)
    {
        BYTE alpha = m_bHasAlpha ? m_buffer[i * bpp + 3] : static_cast<BYTE>(0xFF); // Alpha channel
        rgba.emplace_back(m_buffer[i * bpp + 0]);                                   // Red
        rgba.emplace_back(m_buffer[i * bpp + 1]);                                   // Green
        rgba.emplace_back(m_buffer[i * bpp + 2]);                                   // Blue
        rgba.emplace_back(alpha);                                                   // Alpha
    }

    liq_attr *attr = liq_attr_create();
    if (!attr)
    {
        std::cerr << "Error: Failed to create liq_attr." << std::endl;
        delete[] m_buffer;
        m_buffer = nullptr;
        return false;
    }
    liq_set_quality(attr, 0, 100);
    liq_set_max_colors(attr, 256);

    liq_image *image = liq_image_create_rgba(attr, rgba.data(), m_width, m_height, 0.0);
    if (!image)
    {
        std::cerr << "Error: Failed to create liq_image." << std::endl;
        liq_attr_destroy(attr);
        delete[] m_buffer;
        m_buffer = nullptr;
        return false;
    }

    liq_result *res;
    liq_error quant_error = liq_image_quantize(image, attr, &res);
    if (quant_error != LIQ_OK)
    {
        std::cerr << "Error: Quantization failed with error code " << quant_error << std::endl;
        liq_image_destroy(image);
        liq_attr_destroy(attr);
        delete[] m_buffer;
        m_buffer = nullptr;
        return false;
    }

    const liq_palette *palette = liq_get_palette(res);

    int colors_generated = palette->count;
    if (colors_generated > 256)
    {
        std::cerr << "Warning: Quantized palette has more than 256 colors, m_palette will be truncated." << std::endl;
    }

    for (int i = 0; i < colors_generated; ++i)
    {
        const liq_color &color = palette->entries[i];
        m_palette[i * 3 + 0] = color.r;
        m_palette[i * 3 + 1] = color.g;
        m_palette[i * 3 + 2] = color.b;
    }

    // for (int i = color; i < 256; ++i) {
    //	m_palette[i * 3 + 0] = 0; // Red
    //	m_palette[i * 3 + 1] = 0; // Green
    //	m_palette[i * 3 + 2] = 0; // Blue
    // }

    DWORD bytes = colors_generated * 3;
    if (colors_generated < 256)
    {
        memset(&m_palette[bytes], 0, sizeof(BYTE) * 3 * 256 - bytes);
    }

    std::vector<BYTE> indices(pixelCount);
    liq_error remap_error = liq_write_remapped_image(res, image, indices.data(), indices.size());
    if (remap_error != LIQ_OK)
    {
        std::cerr << "Error: Remapping failed with error code " << remap_error << std::endl;
        liq_result_destroy(res);
        liq_image_destroy(image);
        liq_attr_destroy(attr);
        delete[] m_buffer;
        m_buffer = nullptr;
        return false;
    }

    liq_result_destroy(res);
    liq_image_destroy(image);
    liq_attr_destroy(attr);

    BYTE *alphaBuffer = nullptr;
    if (m_bHasAlpha)
    {
        // Save alpha.
        alphaBuffer = new BYTE[pixelCount];
        for (DWORD ii = 0; ii < pixelCount; ++ii)
        {
            alphaBuffer[ii] = m_buffer[ii * 4 + 3];
        }
    }

    bpp = m_bHasAlpha ? 2 : 1;
    if (!AllocateBuffer(pixelCount * bpp))
    {
        return false;
    }

    // Set the new image.
    if (m_bHasAlpha)
    {
        std::memcpy(m_buffer, indices.data(), pixelCount);
        std::memcpy(&m_buffer[pixelCount], alphaBuffer, pixelCount);
        delete[] alphaBuffer;
		alphaBuffer = nullptr;
    }
    else
    {
        memcpy(m_buffer, indices.data(), pixelCount);
    }

    m_bPalettized = true;

    return true;
}

void MemImage::Depalettize()
{
    if (!m_bPalettized)
        return;

    // Allocate new buffer.
    DWORD bpp = m_bHasAlpha ? 4 : 3;
    DWORD pixelCount = m_width * m_height;
    DWORD bufferNewBytes = pixelCount * bpp;
    BYTE *bufferNew = new BYTE[bufferNewBytes];

    DWORD bufferIx = 0;
    for (DWORD ii = 0; ii < pixelCount; ++ii)
    {
        for (WORD colorIx = 0; colorIx < 3; ++colorIx)
        {
            // The *3 is because m_palette isn't a 2-dimensional array.
            bufferNew[bufferIx++] = m_palette[m_buffer[ii] * 3 + colorIx];
        }

        if (m_bHasAlpha)
            bufferNew[bufferIx++] = m_buffer[pixelCount + ii];
    }

    // Apply new buffer.
    delete[] m_buffer;
    m_buffer = bufferNew;
    m_bufferBytes = bufferNewBytes;
    m_bPalettized = false;
}

bool MemImage::RemoveAlpha()
{
    if (!m_bHasAlpha)
        return true;

    DWORD pixelCount = m_width * m_height;
    DWORD newBytes = 0;
    BYTE *newBuffer = nullptr;
    if (m_bPalettized)
    {
        // Strip the alpha data off the end of the buffer.
        newBytes = pixelCount;
        newBuffer = new BYTE[newBytes];
        if (nullptr == newBuffer)
        {
            std::cout << "ERROR: Couldn't allocate memory.\n";
            return false;
        }

        std::memcpy(newBuffer, m_buffer, newBytes);
    }
    else
    {
        // Convert to an RGB buffer.
        newBytes = pixelCount * 3;
        newBuffer = new BYTE[newBytes];
        if (nullptr == newBuffer)
        {
            std::cout << "ERROR: Couldn't allocate memory.\n";
            return false;
        }

        for (DWORD ii = 0; ii < pixelCount; ++ii)
        {
            newBuffer[ii * 3 + 0] = m_buffer[ii * 4 + 0];
            newBuffer[ii * 3 + 1] = m_buffer[ii * 4 + 1];
            newBuffer[ii * 3 + 2] = m_buffer[ii * 4 + 2];
        }
    }
    delete[] m_buffer;
    m_buffer = newBuffer;
    m_bufferBytes = newBytes;

    m_bHasAlpha = false;
    return true;
}

bool MemImage::AddAlpha()
{
    if (m_bHasAlpha)
    {
        return true;
    }

    DWORD pixelCount = m_width * m_height;
    DWORD newBytes = 0;
    std::vector<BYTE> newBuffer;
    if (m_bPalettized)
    {
        // Strip the alpha data off the end of the buffer.
        newBytes = pixelCount * 2;
        newBuffer.resize(newBytes);

        // Copy the palettized data.
        std::memcpy(newBuffer.data(), m_buffer, pixelCount);
        std::memset(newBuffer.data() + pixelCount, 0xFF, pixelCount);
    }
    else
    {
        // Convert to an RGBA buffer.
        newBytes = pixelCount * 4;
        newBuffer.reserve(newBytes);

        for (DWORD ii = 0; ii < pixelCount; ++ii)
        {
            newBuffer.emplace_back(m_buffer[ii * 3 + 0]);
            newBuffer.emplace_back(m_buffer[ii * 3 + 1]);
            newBuffer.emplace_back(m_buffer[ii * 3 + 2]);
            newBuffer.emplace_back(static_cast<BYTE>(255));
        }
    }

    if (!AllocateBuffer(newBytes))
    {
        return false;
    }

    std::memcpy(m_buffer, newBuffer.data(), newBytes);
    m_bufferBytes = newBytes;
    m_bHasAlpha = true;
    return true;
}

bool MemImage::SaveMipDebugImage(const char *baseFilename, const MemImage *mips, int mipCount)
{
    Clear();
    m_width = mips->m_width;
    m_bHasAlpha = mips->m_bHasAlpha;
    m_bPalettized = mips->m_bPalettized;
    if (m_bPalettized)
        std::memcpy(m_palette, mips->m_palette, MEMIMAGE_PALETTEBYTES);

    // Calculate height of debug image.
    m_height = 0;
    int ii;
    for (ii = 0; ii < mipCount; ++ii)
        m_height += mips[ii].m_height;

    int bytesPerPixel = 1;
    if (!m_bPalettized)
    {
        if (m_bHasAlpha)
            bytesPerPixel = 4;
        else
            bytesPerPixel = 3;
    }

    // Create the buffer for the image.
    DWORD pitch = m_width * bytesPerPixel;
    if (!AllocateBuffer(m_height * pitch * (m_bPalettized && m_bHasAlpha ? 2 : 1)))
        return false;
    std::memset(m_buffer, 0, m_bufferBytes);

    // Create the image.
    DWORD destRow = 0;
    // Note: alphaOffsets only valid/used if pal+alpha.
    DWORD alphaOffset = m_width * m_height;
    for (ii = 0; ii < mipCount; ++ii)
    {
        const MemImage *mip = &mips[ii];

        DWORD mipAlphaOffset = mip->m_width * mip->m_height;
        for (DWORD row = 0; row < mip->m_height; ++row, ++destRow)
        {
            std::memcpy(&m_buffer[destRow * pitch],
                     &mip->m_buffer[row * mip->m_width * bytesPerPixel],
                     mip->m_width * bytesPerPixel);
            if (m_bPalettized && m_bHasAlpha)
            {
                std::memcpy(&m_buffer[alphaOffset + destRow * pitch],
                         &mip->m_buffer[mipAlphaOffset + row * mip->m_width * bytesPerPixel],
                         mip->m_width * bytesPerPixel);
            }
        }
    }

    char pszMipFilename[MAX_PATH];
    sprintf(pszMipFilename, "%s__mips.png", baseFilename);

    return SaveToPNG(pszMipFilename);
}

inline static int GetSourceIndex(int x, int y, DWORD width, DWORD height, int channel, int bpp)
{
    x = std::clamp(x, 0, static_cast<int>(width - 1));
    y = std::clamp(y, 0, static_cast<int>(height - 1));
    return (y * width + x) * bpp + channel;
}

constexpr float ALPHA_EPSILON = 0.01f;

static BYTE ClampToByte(float value)
{
    return static_cast<BYTE>(std::clamp(std::lroundf(value), 0l, 255l));
}

bool MemImage::BuildMipmap(const MemImage& sourceMip)
{
    Clear();

    m_bHasAlpha = sourceMip.m_bHasAlpha;
    m_bPalettized = sourceMip.m_bPalettized;
    m_width = __max(sourceMip.m_width / 2, 1);
    m_height = __max(sourceMip.m_height / 2, 1);

    if (!AllocateBuffer(0))
        return false;

    if (m_bPalettized)
        this->BuildMipmapPalettized(sourceMip);
    else
        this->BuildMipmapRGB(sourceMip);

    return true;
}

void MemImage::BuildMipmapRGB(const MemImage& sourceMip)
{
    const BYTE* src = sourceMip.m_buffer;
    BYTE* dst = this->m_buffer;
    const DWORD srcW = sourceMip.m_width, srcH = sourceMip.m_height;
    const DWORD dstW = this->m_width, dstH = this->m_height;
    const int bpp = this->m_bHasAlpha ? 4 : 3;

    for (DWORD j = 0; j < dstH; ++j)
    {
        for (DWORD i = 0; i < dstW; ++i)
        {
            float weights[4] = { 0.25f, 0.25f, 0.25f, 0.25f };
            float alphas[4] = {};
            float totalAlpha = 0.0f;

            if (this->m_bHasAlpha)
            {
                for (int k = 0; k < 4; ++k)
                {
                    int sx = i * 2 + SO[k][0];
                    int sy = j * 2 + SO[k][1];
                    BYTE a = src[GetSourceIndex(sx, sy, srcW, srcH, 3, bpp)];
                    alphas[k] = float(a) / 255.0f;
                    totalAlpha += alphas[k];
                }
                if (totalAlpha > ALPHA_EPSILON)
                {
                    for (int k = 0; k < 4; ++k)
                        weights[k] = alphas[k] / totalAlpha;
                }
            }

            for (int c = 0; c < bpp; ++c)
            {
                float acc = 0.0f;
                for (int k = 0; k < 4; ++k)
                {
                    int sx = i * 2 + SO[k][0];
                    int sy = j * 2 + SO[k][1];
                    acc += src[GetSourceIndex(sx, sy, srcW, srcH, c, bpp)] * weights[k];
                }
                dst[GetSourceIndex(i, j, dstW, dstH, c, bpp)] = ClampToByte(acc);
            }
        }
    }
}

void MemImage::BuildMipmapPalettized(const MemImage& sourceMip)
{
    const BYTE* src = sourceMip.m_buffer;
    BYTE* dst = this->m_buffer;
    const DWORD srcW = sourceMip.m_width, srcH = sourceMip.m_height;
    const DWORD dstW = this->m_width, dstH = this->m_height;
    const int bpp = 1;  // Palettized means 1 byte per pixel index

    for (DWORD j = 0; j < dstH; ++j)
    {
        for (DWORD i = 0; i < dstW; ++i)
        {
            png_color colors[4]{};
            BYTE indices[4]{};
            float weights[4] = { 0.25f, 0.25f, 0.25f, 0.25f };
            float alphas[4] = {};
            float totalAlpha = 0.0f;

            for (int k = 0; k < 4; ++k)
            {
                int sx = i * 2 + SO[k][0];
                int sy = j * 2 + SO[k][1];
                int idx = GetSourceIndex(sx, sy, srcW, srcH, 0, bpp);
                BYTE palIx = src[idx];
                indices[k] = palIx;
                colors[k] = {
                    this->m_palette[palIx * 3 + 0],
                    this->m_palette[palIx * 3 + 1],
                    this->m_palette[palIx * 3 + 2]
                };

                if (this->m_bHasAlpha)
                {
                    alphas[k] = float(src[srcW * srcH + idx]) / 255.0f;
                    totalAlpha += alphas[k];
                }
            }

            if (this->m_bHasAlpha && totalAlpha > ALPHA_EPSILON)
            {
                for (int k = 0; k < 4; ++k)
                    weights[k] = alphas[k] / totalAlpha;
            }

            float r = 0, g = 0, b = 0;
            for (int k = 0; k < 4; ++k)
            {
                r += colors[k].red * weights[k];
                g += colors[k].green * weights[k];
                b += colors[k].blue * weights[k];
            }
            png_color avgColor = { ClampToByte(r), ClampToByte(g), ClampToByte(b) };

            int minDelta = INT32_MAX;
            int bestIx = 0;
            for (int k = 0; k < 4; ++k)
            {
                int delta = static_cast<int>(
                    std::abs(colors[k].red - avgColor.red) +
                    std::abs(colors[k].green - avgColor.green) +
                    std::abs(colors[k].blue - avgColor.blue)
                    );
                delta = static_cast<int>(delta * weights[k]);
                if (delta < minDelta)
                {
                    minDelta = delta;
                    bestIx = k;
                }
            }

            dst[GetSourceIndex(i, j, dstW, dstH, 0, bpp)] = indices[bestIx];

            if (this->m_bHasAlpha)
            {
                float avgAlpha = totalAlpha * 255.0f / 4.0f;
                dst[dstW * dstH + GetSourceIndex(i, j, dstW, dstH, 0, bpp)] = ClampToByte(avgAlpha);
            }
        }
    }
}