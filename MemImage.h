#ifndef MEMIMAGE_H
#define MEMIMAGE_H

#ifndef _linux_
#include <algorithm>
#include <iostream>
#include <string>
#include <windows.h>
#else
typedef unsigned char BYTE;
typedef unsigned short WORD;
typedef unsigned long DWORD;
#endif

constexpr WORD MEMIMAGE_PALETTEBYTES = 3 * 256;

/*
enum BLPType
{
	BLPTYPE_UNSPECIFIED,
	BLPTYPE_PAL_ALPHA0,		// compression = 0, alphaBitDepth = 0
	BLPTYPE_PAL_ALPHA1,		// compression = 0, alphaBitDepth = 1
	BLPTYPE_PAL_ALPHA4,		// compression = 0, alphaBitDepth = 4	- This has been seen but only on alpha-less textures.
	BLPTYPE_PAL_ALPHA8,		// compression = 0, alphaBitDepth = 8
	BLPTYPE_DXT1_ALPHA0,	// (DXT1) compression = 1, alphaBitDepth = 0
	BLPTYPE_DXT1_ALPHA1,	// (DXT1) compression = 1, alphaBitDepth = 1
	BLPTYPE_DXT3,			// (DXT3) compression = 1, alphaBitDepth = 8
	BLPTYPE_DXT5,			// (DXT5) compression = 1, alphaBitDepth = 8, alphaUnknown = 7
	BLPTYPE_COUNT
};
// NOTE: These arrays need to be updated when the above enum changes.
const char* BLPTypeDescriptions[];
const char* BLPTypeNames[];

enum PNGType
{
	PNGTYPE_UNSPECIFIED,
	PNGTYPE_PALETTIZED,
	PNGTYPE_PALETTIZED_ALPHAMASK,
	PNGTYPE_RGB,
	PNGTYPE_RGBA,
	PNGTYPE_COUNT
};
// NOTE: These arrays need to be updated when the above enum changes.
const char* PNGTypeDescriptions[];
const char* PNGTypeNames[];
*/

enum FORMATID
{
	FORMAT_UNSPECIFIED,

	BLPTYPE_FIRST,
	BLPTYPE_PAL_ALPHA0 = BLPTYPE_FIRST, // 1 - compression = 0, alphaBitDepth = 0
	BLPTYPE_PAL_ALPHA1,					// 2 - compression = 0, alphaBitDepth = 1
	BLPTYPE_PAL_ALPHA4,					// 3 - compression = 0, alphaBitDepth = 4
	BLPTYPE_PAL_ALPHA8,					// 4 - compression = 0, alphaBitDepth = 8
	BLPTYPE_DXT1_ALPHA0,				// 5 - (DXT1) compression = 1, alphaBitDepth = 0
	BLPTYPE_DXT1_ALPHA1,				// 6 - (DXT1) compression = 1, alphaBitDepth = 1
	BLPTYPE_DXT3,						// 7 - (DXT3) compression = 1, alphaBitDepth = 8
	BLPTYPE_DXT5,						// 8 - (DXT5) compression = 1, alphaBitDepth = 8, alphaUnknown = 7
	BLPTYPE_BGRA,						// 9 - (BGRa) compression = 2
	BLPTYPE_LAST = BLPTYPE_BGRA,

	PNGTYPE_FIRST,
	PNGTYPE_PALETTIZED = PNGTYPE_FIRST,
	PNGTYPE_PALETTIZED_ALPHAMASK,
	PNGTYPE_RGB,
	PNGTYPE_RGBA,
	PNGTYPE_LAST = PNGTYPE_RGBA,

	FORMAT_COUNT
};
const char *FORMATIDNames[];
const char *FORMATIDDescriptions[];

#define ISBLP(format) (BLPTYPE_FIRST <= format && format <= BLPTYPE_LAST)
#define ISPNG(format) (PNGTYPE_FIRST <= format && format <= PNGTYPE_LAST)

enum FileType
{
	FILETYPE_UNSPECIFIED,
	FILETYPE_PNG,
	FILETYPE_BLP,
	FILETYPE_COUNT
};

// BLP Load result.  Used as program return code if != 0.
enum LoadResult
{
	LOADRESULT_SUCCESS = 0,
	LOADRESULT_PATCH = -1,	   // .blp is a patch file
	LOADRESULT_NOTBLP = -2,	   // Bad BLP2 id.
	LOADRESULT_VERSION = -3,   // Header version field unrecognized.
	LOADRESULT_MEMORY = -4,	   // OOM
	LOADRESULT_ERROR = -5,	   // Generic error
	LOADRESULT_FILEERROR = -6, // File system error
};

class MemImage
{
public:
	// Public settings.
	static bool s_bCreateMipTestImage;
	static float s_fGammaFactor; // Legacy.
	static BYTE s_byAlphaThreshold;
	static bool s_bVerbose;
	static bool s_bNoMips;
	static FORMATID s_ruleTable[FORMAT_COUNT];
	static bool s_bOnlyWarnOnPatch;

protected:
	DWORD m_width;
	DWORD m_height;

	// These two variables define the 4 formats of data MemImage can contain.
	bool m_bPalettized;
	bool m_bHasAlpha;

	// This contains the actual data in the following formats:
	// - m_bPalettized == true && m_bHasAlpha = false -> 1-byte/entry
	// - m_bPalettized == true && m_bHasAlpha = true -> 1-byte/entry, then 1-alpha byte/entry [image buffer][alpha buffer]
	// - m_bPalettized == false && m_bHasAlpha == false -> 3 bpp rgb
	// - m_bPalettized == false && m_bHasAlpha == true -> 4 bpp rgba
	BYTE* m_buffer;
	size_t m_bufferBytes;

	BYTE m_palette[MEMIMAGE_PALETTEBYTES]; // RGB

public:
	MemImage();
	MemImage(const MemImage &rhs);
	~MemImage();
	MemImage &operator=(const MemImage &rhs);
	// bool Init(DWORD width, DWORD height, bool hasAlpha, bool palettized);
	void Copy(const MemImage &rhs);
	void Clear();
	// If bytes == 0 then it calculates the proper size with the CalculateBufferBytes function.
	bool AllocateBuffer(size_t bytes);

	// Load functions.
	LoadResult LoadFromBLP(const char *filename, FORMATID *blpTypeRet = nullptr);
	bool LoadFromPNG(const char *filename, FORMATID *pngTypeRet = nullptr);

	// Save functions.  All are guaranteed not to change the image data at all.  If conversions
	// need to be made a temp image will be created.
	bool Save(const char *filename, FORMATID type) const;
	bool SaveToBLP(const char *filename, FORMATID type = FORMAT_UNSPECIFIED) const;
	bool SaveToPNG(const char *filename, FORMATID type = FORMAT_UNSPECIFIED) const;

	// If palettizes or ff already palettized returns true.  Returns false on error.
	bool Palettize();
	// Does nothing if not palettized.
	void Depalettize();

	// Returns false on error, true otherwise.  No error if it does nothing.
	bool RemoveAlpha();
	bool AddAlpha();

	bool BuildMipmap(const MemImage &sourceMip);
	bool SaveMipDebugImage(const char *baseFilename, const MemImage *mips, int mipCount);

private:
	void BuildMipmapRGB(const MemImage& sourceMip);
	void BuildMipmapPalettized(const MemImage& sourceMip);

public:
	static DWORD CalculateBufferBytes(DWORD width, DWORD height, bool bHasAlpha, bool bPalettized);
};

#endif // MEMIMAGE_H