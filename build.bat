@echo off
REM BLPConverter CMake Build Script
REM This script provides easy commands to build the project using CMake

setlocal enabledelayedexpansion

if "%1"=="" goto :usage
if "%1"=="help" goto :usage
if "%1"=="-h" goto :usage
if "%1"=="--help" goto :usage

if "%1"=="clean" goto :clean
if "%1"=="debug" goto :debug
if "%1"=="release" goto :release
if "%1"=="both" goto :both
if "%1"=="test" goto :test

echo Unknown command: %1
goto :usage

:usage
echo.
echo BLPConverter CMake Build Script
echo.
echo Usage: build.bat [command]
echo.
echo Commands:
echo   debug     - Build Debug version
echo   release   - Build Release version  
echo   both      - Build both Debug and Release versions
echo   clean     - Clean all build directories
echo   test      - Test the built executables
echo   help      - Show this help message
echo.
echo Examples:
echo   build.bat debug
echo   build.bat release
echo   build.bat both
echo.
goto :end

:clean
echo Cleaning build directories...
if exist build rmdir /s /q build
echo Build directories cleaned.
goto :end

:debug
echo Building Debug version...
cmake --preset x64-debug
if errorlevel 1 (
    echo Configuration failed!
    goto :end
)
cmake --build --preset x64-debug
if errorlevel 1 (
    echo Build failed!
    goto :end
)
echo Debug build completed successfully!
echo Executable: build\x64-debug\Debug\BLPConverterd.exe
goto :end

:release
echo Building Release version...
cmake --preset x64-release
if errorlevel 1 (
    echo Configuration failed!
    goto :end
)
cmake --build --preset x64-release
if errorlevel 1 (
    echo Build failed!
    goto :end
)
echo Release build completed successfully!
echo Executable: build\x64-release\Release\BLPConverter.exe
goto :end

:both
echo Building both Debug and Release versions...
call :debug
if errorlevel 1 goto :end
call :release
if errorlevel 1 goto :end
echo Both builds completed successfully!
goto :end

:test
echo Testing built executables...
if exist "build\x64-debug\Debug\BLPConverterd.exe" (
    echo Testing Debug version:
    "build\x64-debug\Debug\BLPConverterd.exe" /L
    echo.
) else (
    echo Debug executable not found. Run 'build.bat debug' first.
)

if exist "build\x64-release\Release\BLPConverter.exe" (
    echo Testing Release version:
    "build\x64-release\Release\BLPConverter.exe" /L
    echo.
) else (
    echo Release executable not found. Run 'build.bat release' first.
)
goto :end

:end
endlocal
