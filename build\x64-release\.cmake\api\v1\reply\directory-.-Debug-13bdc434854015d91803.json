{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 338, "parent": 0}, {"command": 0, "file": 0, "line": 345, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Runtime", "destination": "bin", "paths": ["Debug/BLPConverterd.exe"], "targetId": "BLPConverter::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 2, "component": "Development", "destination": "lib", "paths": ["Debug/zlibd.lib"], "targetId": "zlib::@6890427a1f51a3e7e1df", "targetIndex": 5, "type": "target"}, {"backtrace": 2, "component": "Development", "destination": "lib", "paths": ["Debug/libpng16d.lib"], "targetId": "libpng16::@6890427a1f51a3e7e1df", "targetIndex": 3, "type": "target"}, {"backtrace": 2, "component": "Development", "destination": "lib", "paths": ["Debug/squishd.lib"], "targetId": "squish::@6890427a1f51a3e7e1df", "targetIndex": 4, "type": "target"}], "paths": {"build": ".", "source": "."}}