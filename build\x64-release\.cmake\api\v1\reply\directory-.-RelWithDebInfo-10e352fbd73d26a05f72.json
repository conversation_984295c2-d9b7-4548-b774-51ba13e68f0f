{"backtraceGraph": {"commands": ["install"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 327, "parent": 0}, {"command": 0, "file": 0, "line": 334, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Runtime", "destination": "bin", "paths": ["RelWithDebInfo/BLPConverter.exe"], "targetId": "BLPConverter::@6890427a1f51a3e7e1df", "targetIndex": 1, "type": "target"}, {"backtrace": 2, "component": "Development", "destination": "lib", "paths": ["RelWithDebInfo/zlib.lib"], "targetId": "zlib::@6890427a1f51a3e7e1df", "targetIndex": 5, "type": "target"}, {"backtrace": 2, "component": "Development", "destination": "lib", "paths": ["RelWithDebInfo/libpng16.lib"], "targetId": "libpng16::@6890427a1f51a3e7e1df", "targetIndex": 3, "type": "target"}, {"backtrace": 2, "component": "Development", "destination": "lib", "paths": ["RelWithDebInfo/squish.lib"], "targetId": "squish::@6890427a1f51a3e7e1df", "targetIndex": 4, "type": "target"}], "paths": {"build": ".", "source": "."}}