{"artifacts": [{"path": "MinSizeRel/BLPConverter.exe"}, {"path": "MinSizeRel/BLPConverter.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 268, "parent": 0}, {"command": 1, "file": 0, "line": 338, "parent": 0}, {"command": 2, "file": 0, "line": 279, "parent": 0}, {"command": 2, "file": 0, "line": 287, "parent": 0}, {"command": 2, "file": 0, "line": 310, "parent": 0}, {"command": 3, "file": 0, "line": 271, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -std:c++20 -MD"}], "includes": [{"backtrace": 6, "path": "D:/dev/BLPConverter/../squish-1.11"}, {"backtrace": 6, "path": "D:/dev/BLPConverter/../lpng1648"}, {"backtrace": 6, "path": "D:/dev/BLPConverter/../zlib-1.3.1"}, {"backtrace": 6, "isSystem": true, "path": "D:/dev/BLPConverter"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 1]}], "dependencies": [{"backtrace": 3, "id": "zlib::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "libpng16::@6890427a1f51a3e7e1df"}, {"backtrace": 3, "id": "squish::@6890427a1f51a3e7e1df"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "BLPConverter::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "D:/dev/BLPConverter/install/x64-release"}}, "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /O1 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 3, "fragment": "MinSizeRel\\squish.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "MinSizeRel\\libpng16.lib", "role": "libraries"}, {"backtrace": 3, "fragment": "MinSizeRel\\zlib.lib", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\dev\\BLPConverter\\Release\\imagequant_sys.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "kernel32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "user32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "gdi32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "winspool.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "advapi32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "ole32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "oleaut32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "uuid.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "odbc32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "odbccp32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "Ws2_32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "Userenv.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "ntdll.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "BLPConverter", "nameOnDisk": "BLPConverter.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 3, 4, 5]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "BLPConverter.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "MemImage.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "BLP.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "MemImage.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "libimagequant.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "port.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}