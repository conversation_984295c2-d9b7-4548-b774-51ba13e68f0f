{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "RelWithDebInfo/libpng16.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_link_libraries", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 144, "parent": 0}, {"command": 1, "file": 0, "line": 345, "parent": 0}, {"command": 2, "file": 0, "line": 147, "parent": 0}, {"command": 3, "file": 0, "line": 145, "parent": 0}, {"command": 3, "file": 0, "line": 146, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /Zi /O2 /Ob1 /DNDEBUG -std:c17 -MD"}], "includes": [{"backtrace": 4, "path": "D:/dev/BLPConverter/../lpng1648"}, {"backtrace": 5, "path": "D:/dev/BLPConverter/../zlib-1.3.1"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "dependencies": [{"backtrace": 3, "id": "zlib::@6890427a1f51a3e7e1df"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "libpng16::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "D:/dev/BLPConverter/install/x64-release"}}, "name": "libpng16", "nameOnDisk": "libpng16.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/png.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngerror.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngget.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngmem.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngpread.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngread.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngrio.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngrtran.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngrutil.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngset.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngtrans.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngwio.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngwrite.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngwtran.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/pngwutil.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/arm/arm_init.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/arm/filter_neon_intrinsics.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/lpng1648/arm/palette_neon_intrinsics.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}