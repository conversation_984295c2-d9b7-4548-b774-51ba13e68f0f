{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "Debug/squishd.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "target_compile_options", "target_compile_definitions", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 192, "parent": 0}, {"command": 1, "file": 0, "line": 334, "parent": 0}, {"command": 2, "file": 0, "line": 202, "parent": 0}, {"command": 3, "file": 0, "line": 196, "parent": 0}, {"command": 4, "file": 0, "line": 193, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /GR /EHsc /Zi /Ob0 /Od /RTC1 -std:c++20 -MDd"}, {"backtrace": 3, "fragment": "/Od"}, {"backtrace": 3, "fragment": "/RTC1"}, {"backtrace": 3, "fragment": "/openmp"}], "defines": [{"backtrace": 4, "define": "SQUISH_USE_OPENMP"}, {"backtrace": 4, "define": "WIN32"}, {"backtrace": 4, "define": "_DEBUG"}, {"backtrace": 4, "define": "_WINDOWS"}], "includes": [{"backtrace": 5, "path": "D:/dev/BLPConverter/../squish-1.11"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "squish::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "D:/dev/BLPConverter/install/x64-release"}}, "name": "squish", "nameOnDisk": "squishd.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/alpha.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/clusterfit.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/colourblock.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/colourfit.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/colourset.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/maths.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/rangefit.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/singlecolourfit.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/dev/squish-1.11/squish.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "D:/dev/squish-1.11/alpha.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/clusterfit.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/colourblock.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/colourfit.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/colourset.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/maths.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/rangefit.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/simd.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/simd_float.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/simd_sse.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/simd_ve.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/singlecolourfit.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/singlecolourlookup.inl", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "D:/dev/squish-1.11/squish.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}