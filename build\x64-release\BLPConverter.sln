﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}"
	ProjectSection(ProjectDependencies) = postProject
		{8A838968-4642-3A9E-8CDC-47B16F413305} = {8A838968-4642-3A9E-8CDC-47B16F413305}
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D} = {FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "BLPConverter", "BLPConverter.vcxproj", "{8A838968-4642-3A9E-8CDC-47B16F413305}"
	ProjectSection(ProjectDependencies) = postProject
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8} = {8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D} = {FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D} = {9A7AE529-A0AE-3857-81A6-5A3242273E7D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{647D8281-B5BD-308B-AC68-4E1057AC0468}"
	ProjectSection(ProjectDependencies) = postProject
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F} = {FB91F347-6C74-39FD-A8FA-F63AB2A5535F}
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "PACKAGE", "PACKAGE.vcxproj", "{BE0C60C9-0D90-369F-9221-5D1D4B94F693}"
	ProjectSection(ProjectDependencies) = postProject
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F} = {FB91F347-6C74-39FD-A8FA-F63AB2A5535F}
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{4BA12E99-A490-3390-AC67-0B107297ADE0}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "png_genfiles", "libpng\png_genfiles.vcxproj", "{C37E5B7E-1D22-3924-8507-3203D711312E}"
	ProjectSection(ProjectDependencies) = postProject
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "png_static", "libpng\png_static.vcxproj", "{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}"
	ProjectSection(ProjectDependencies) = postProject
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
		{C37E5B7E-1D22-3924-8507-3203D711312E} = {C37E5B7E-1D22-3924-8507-3203D711312E}
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D} = {9A7AE529-A0AE-3857-81A6-5A3242273E7D}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "squish", "squish.vcxproj", "{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}"
	ProjectSection(ProjectDependencies) = postProject
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "zlibstatic", "zlib\zlibstatic.vcxproj", "{9A7AE529-A0AE-3857-81A6-5A3242273E7D}"
	ProjectSection(ProjectDependencies) = postProject
		{4BA12E99-A490-3390-AC67-0B107297ADE0} = {4BA12E99-A490-3390-AC67-0B107297ADE0}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.Debug|x64.ActiveCfg = Debug|x64
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.Debug|x64.Build.0 = Debug|x64
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.Release|x64.ActiveCfg = Release|x64
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.Release|x64.Build.0 = Release|x64
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FB91F347-6C74-39FD-A8FA-F63AB2A5535F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.Debug|x64.ActiveCfg = Debug|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.Debug|x64.Build.0 = Debug|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.Release|x64.ActiveCfg = Release|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.Release|x64.Build.0 = Release|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8A838968-4642-3A9E-8CDC-47B16F413305}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{647D8281-B5BD-308B-AC68-4E1057AC0468}.Debug|x64.ActiveCfg = Debug|x64
		{647D8281-B5BD-308B-AC68-4E1057AC0468}.Release|x64.ActiveCfg = Release|x64
		{647D8281-B5BD-308B-AC68-4E1057AC0468}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{647D8281-B5BD-308B-AC68-4E1057AC0468}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BE0C60C9-0D90-369F-9221-5D1D4B94F693}.Debug|x64.ActiveCfg = Debug|x64
		{BE0C60C9-0D90-369F-9221-5D1D4B94F693}.Release|x64.ActiveCfg = Release|x64
		{BE0C60C9-0D90-369F-9221-5D1D4B94F693}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BE0C60C9-0D90-369F-9221-5D1D4B94F693}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.Debug|x64.ActiveCfg = Debug|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.Debug|x64.Build.0 = Debug|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.Release|x64.ActiveCfg = Release|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.Release|x64.Build.0 = Release|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4BA12E99-A490-3390-AC67-0B107297ADE0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.Debug|x64.ActiveCfg = Debug|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.Debug|x64.Build.0 = Debug|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.Release|x64.ActiveCfg = Release|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.Release|x64.Build.0 = Release|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C37E5B7E-1D22-3924-8507-3203D711312E}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.Debug|x64.ActiveCfg = Debug|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.Debug|x64.Build.0 = Debug|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.Release|x64.ActiveCfg = Release|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.Release|x64.Build.0 = Release|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8F8ABA90-D176-3FCA-847A-C6667D7BE7F8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.Debug|x64.ActiveCfg = Debug|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.Debug|x64.Build.0 = Debug|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.Release|x64.ActiveCfg = Release|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.Release|x64.Build.0 = Release|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{FB9F4C55-E492-36CE-8604-9B8A98BBFA8D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.Debug|x64.ActiveCfg = Debug|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.Debug|x64.Build.0 = Debug|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.Release|x64.ActiveCfg = Release|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.Release|x64.Build.0 = Release|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9A7AE529-A0AE-3857-81A6-5A3242273E7D}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A2FA964C-D343-34CC-BDE5-50C7F2389E96}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
