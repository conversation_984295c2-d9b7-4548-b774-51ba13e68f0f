set(CMAKE_ASM_COMPILER "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe")
set(CMAKE_ASM_COMPILER_AR "")
set(CMAKE_RANLIB ":")
set(CMAKE_ASM_COMPILER_RANLIB "")
set(CMAKE_LINKER "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe")
set(CMAKE_LINKER_LINK "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe")
set(CMAKE_LINKER_LLD "lld-link")
set(CMAKE_ASM_COMPILER_LINKER "")
set(CMAKE_ASM_COMPILER_LINKER_ID "")
set(CMAKE_ASM_COMPILER_LINKER_VERSION )
set(CMAKE_ASM_COMPILER_LINKER_FRONTEND_VARIANT )
set(CMAKE_MT "CMAKE_MT-NOTFOUND")
set(CMAKE_TAPI "")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "MSVC")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)
set(CMAKE_ASM_LINKER_DEPFILE_SUPPORTED )


