
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.22621 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:02:01銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\3.31.6-msvc6\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\3.31.6-msvc6\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\3.31.6-msvc6\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.86
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/dev/BLPConverter/build/x64-release/CMakeFiles/3.31.6-msvc6/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:02:03銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.91
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/dev/BLPConverter/build/x64-release/CMakeFiles/3.31.6-msvc6/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-khpg47"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-khpg47"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-khpg47'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_843ff.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:02:04銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-khpg47\\cmTC_843ff.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_843ff.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-khpg47\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_843ff.dir\\Debug\\cmTC_843ff.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_843ff.dir\\Debug\\cmTC_843ff.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_843ff.dir\\Debug\\cmTC_843ff.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_843ff.dir\\Debug\\\\" /Fd"cmTC_843ff.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_843ff.dir\\Debug\\\\" /Fd"cmTC_843ff.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-khpg47\\Debug\\cmTC_843ff.exe" /INCREMENTAL /ILK:"cmTC_843ff.dir\\Debug\\cmTC_843ff.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-khpg47/Debug/cmTC_843ff.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-khpg47/Debug/cmTC_843ff.lib" /MACHINE:X64  /machine:x64 cmTC_843ff.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_843ff.vcxproj -> D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-khpg47\\Debug\\cmTC_843ff.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_843ff.dir\\Debug\\cmTC_843ff.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_843ff.dir\\Debug\\cmTC_843ff.tlog\\cmTC_843ff.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-khpg47\\cmTC_843ff.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.86
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-t93yom"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-t93yom"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-t93yom'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_cd7c5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:02:06銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-t93yom\\cmTC_cd7c5.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_cd7c5.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-t93yom\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_cd7c5.dir\\Debug\\cmTC_cd7c5.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_cd7c5.dir\\Debug\\cmTC_cd7c5.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_cd7c5.dir\\Debug\\cmTC_cd7c5.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_cd7c5.dir\\Debug\\\\" /Fd"cmTC_cd7c5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_cd7c5.dir\\Debug\\\\" /Fd"cmTC_cd7c5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-t93yom\\Debug\\cmTC_cd7c5.exe" /INCREMENTAL /ILK:"cmTC_cd7c5.dir\\Debug\\cmTC_cd7c5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-t93yom/Debug/cmTC_cd7c5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-t93yom/Debug/cmTC_cd7c5.lib" /MACHINE:X64  /machine:x64 cmTC_cd7c5.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_cd7c5.vcxproj -> D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-t93yom\\Debug\\cmTC_cd7c5.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_cd7c5.dir\\Debug\\cmTC_cd7c5.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_cd7c5.dir\\Debug\\cmTC_cd7c5.tlog\\cmTC_cd7c5.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-t93yom\\cmTC_cd7c5.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.93
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35211.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "D:/dev/zlib-1.3.1/CMakeLists.txt:22 (check_include_file)"
    checks:
      - "Looking for sys/types.h"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ixivl0"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ixivl0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "HAVE_SYS_TYPES_H"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ixivl0'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_fa093.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:12:13銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ixivl0\\cmTC_fa093.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_fa093.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ixivl0\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_fa093.dir\\Debug\\cmTC_fa093.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_fa093.dir\\Debug\\cmTC_fa093.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_fa093.dir\\Debug\\cmTC_fa093.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_fa093.dir\\Debug\\\\" /Fd"cmTC_fa093.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ixivl0\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_fa093.dir\\Debug\\\\" /Fd"cmTC_fa093.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ixivl0\\CheckIncludeFile.c"
          CheckIncludeFile.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ixivl0\\Debug\\cmTC_fa093.exe" /INCREMENTAL /ILK:"cmTC_fa093.dir\\Debug\\cmTC_fa093.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ixivl0/Debug/cmTC_fa093.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ixivl0/Debug/cmTC_fa093.lib" /MACHINE:X64  /machine:x64 cmTC_fa093.dir\\Debug\\CheckIncludeFile.obj
          cmTC_fa093.vcxproj -> D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ixivl0\\Debug\\cmTC_fa093.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_fa093.dir\\Debug\\cmTC_fa093.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_fa093.dir\\Debug\\cmTC_fa093.tlog\\cmTC_fa093.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ixivl0\\cmTC_fa093.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.90
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "D:/dev/zlib-1.3.1/CMakeLists.txt:23 (check_include_file)"
    checks:
      - "Looking for stdint.h"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-mmswyy"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-mmswyy"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "HAVE_STDINT_H"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-mmswyy'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_328ef.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:12:14銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-mmswyy\\cmTC_328ef.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_328ef.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-mmswyy\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_328ef.dir\\Debug\\cmTC_328ef.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_328ef.dir\\Debug\\cmTC_328ef.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_328ef.dir\\Debug\\cmTC_328ef.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_328ef.dir\\Debug\\\\" /Fd"cmTC_328ef.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-mmswyy\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_328ef.dir\\Debug\\\\" /Fd"cmTC_328ef.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-mmswyy\\CheckIncludeFile.c"
          CheckIncludeFile.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-mmswyy\\Debug\\cmTC_328ef.exe" /INCREMENTAL /ILK:"cmTC_328ef.dir\\Debug\\cmTC_328ef.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-mmswyy/Debug/cmTC_328ef.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-mmswyy/Debug/cmTC_328ef.lib" /MACHINE:X64  /machine:x64 cmTC_328ef.dir\\Debug\\CheckIncludeFile.obj
          cmTC_328ef.vcxproj -> D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-mmswyy\\Debug\\cmTC_328ef.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_328ef.dir\\Debug\\cmTC_328ef.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_328ef.dir\\Debug\\cmTC_328ef.tlog\\cmTC_328ef.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-mmswyy\\cmTC_328ef.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.86
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "D:/dev/zlib-1.3.1/CMakeLists.txt:24 (check_include_file)"
    checks:
      - "Looking for stddef.h"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-8ls9d5"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-8ls9d5"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "HAVE_STDDEF_H"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-8ls9d5'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6b6f6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:12:15銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-8ls9d5\\cmTC_6b6f6.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6b6f6.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-8ls9d5\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_6b6f6.dir\\Debug\\cmTC_6b6f6.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_6b6f6.dir\\Debug\\cmTC_6b6f6.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_6b6f6.dir\\Debug\\cmTC_6b6f6.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_6b6f6.dir\\Debug\\\\" /Fd"cmTC_6b6f6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-8ls9d5\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_6b6f6.dir\\Debug\\\\" /Fd"cmTC_6b6f6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-8ls9d5\\CheckIncludeFile.c"
          CheckIncludeFile.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-8ls9d5\\Debug\\cmTC_6b6f6.exe" /INCREMENTAL /ILK:"cmTC_6b6f6.dir\\Debug\\cmTC_6b6f6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-8ls9d5/Debug/cmTC_6b6f6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-8ls9d5/Debug/cmTC_6b6f6.lib" /MACHINE:X64  /machine:x64 cmTC_6b6f6.dir\\Debug\\CheckIncludeFile.obj
          cmTC_6b6f6.vcxproj -> D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-8ls9d5\\Debug\\cmTC_6b6f6.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_6b6f6.dir\\Debug\\cmTC_6b6f6.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_6b6f6.dir\\Debug\\cmTC_6b6f6.tlog\\cmTC_6b6f6.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-8ls9d5\\cmTC_6b6f6.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.93
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckTypeSize.cmake:156 (try_compile)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckTypeSize.cmake:283 (__check_type_size_impl)"
      - "D:/dev/zlib-1.3.1/CMakeLists.txt:42 (check_type_size)"
    checks:
      - "Check size of off64_t"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ofctfb"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ofctfb"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "HAVE_OFF64_T"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-ofctfb'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_cd44b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:12:17銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_cd44b.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_cd44b.dir\\Debug\\cmTC_cd44b.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_cd44b.dir\\Debug\\cmTC_cd44b.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_cd44b.dir\\Debug\\cmTC_cd44b.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _LARGEFILE64_SOURCE=1 /D HAVE_SYS_TYPES_H /D HAVE_STDINT_H /D HAVE_STDDEF_H /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_cd44b.dir\\Debug\\\\" /Fd"cmTC_cd44b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D _LARGEFILE64_SOURCE=1 /D HAVE_SYS_TYPES_H /D HAVE_STDINT_H /D HAVE_STDDEF_H /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_cd44b.dir\\Debug\\\\" /Fd"cmTC_cd44b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c"
          OFF64_T.c
        D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(29,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
        D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(30,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
        D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(31,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
        D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(32,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
        D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(33,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(29,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
          D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(30,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
          D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(31,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
          D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(32,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
          D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\OFF64_T.c(33,12): error C2065: 鈥渙ff64_t鈥? 鏈０鏄庣殑鏍囪瘑绗?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-ofctfb\\cmTC_cd44b.vcxproj]
        
            0 涓鍛?
            5 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.59
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckFunctionExists.cmake:93 (try_compile)"
      - "D:/dev/zlib-1.3.1/CMakeLists.txt:51 (check_function_exists)"
    checks:
      - "Looking for fseeko"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-a0ib9q"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-a0ib9q"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "HAVE_FSEEKO"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-a0ib9q'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_37e26.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:12:18銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\cmTC_37e26.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_37e26.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_37e26.dir\\Debug\\cmTC_37e26.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_37e26.dir\\Debug\\cmTC_37e26.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_37e26.dir\\Debug\\cmTC_37e26.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=fseeko /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_37e26.dir\\Debug\\\\" /Fd"cmTC_37e26.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=fseeko /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_37e26.dir\\Debug\\\\" /Fd"cmTC_37e26.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\Debug\\cmTC_37e26.exe" /INCREMENTAL /ILK:"cmTC_37e26.dir\\Debug\\cmTC_37e26.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-a0ib9q/Debug/cmTC_37e26.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-a0ib9q/Debug/cmTC_37e26.lib" /MACHINE:X64  /machine:x64 cmTC_37e26.dir\\Debug\\CheckFunctionExists.obj
        CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?fseeko锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\cmTC_37e26.vcxproj]
        D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\Debug\\cmTC_37e26.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\cmTC_37e26.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\cmTC_37e26.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\cmTC_37e26.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          CheckFunctionExists.obj : error LNK2019: 鏃犳硶瑙ｆ瀽鐨勫閮ㄧ鍙?fseeko锛屽嚱鏁?main 涓紩鐢ㄤ簡璇ョ鍙?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\cmTC_37e26.vcxproj]
          D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\Debug\\cmTC_37e26.exe : fatal error LNK1120: 1 涓棤娉曡В鏋愮殑澶栭儴鍛戒护 [D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-a0ib9q\\cmTC_37e26.vcxproj]
        
            0 涓鍛?
            2 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.79
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckIncludeFile.cmake:99 (try_compile)"
      - "D:/dev/zlib-1.3.1/CMakeLists.txt:59 (check_include_file)"
    checks:
      - "Looking for unistd.h"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-c7l2v0"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-c7l2v0"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "Z_HAVE_UNISTD_H"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-c7l2v0'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d416a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:12:19銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\cmTC_d416a.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d416a.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d416a.dir\\Debug\\cmTC_d416a.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_d416a.dir\\Debug\\cmTC_d416a.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_d416a.dir\\Debug\\cmTC_d416a.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_d416a.dir\\Debug\\\\" /Fd"cmTC_d416a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\CheckIncludeFile.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_d416a.dir\\Debug\\\\" /Fd"cmTC_d416a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\CheckIncludeFile.c"
          CheckIncludeFile.c
        D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\CheckIncludeFile.c(1,1): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渦nistd.h鈥? No such file or directory [D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\cmTC_d416a.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\cmTC_d416a.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\cmTC_d416a.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\CheckIncludeFile.c(1,1): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥渦nistd.h鈥? No such file or directory [D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-c7l2v0\\cmTC_d416a.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.56
        
      exitCode: 1
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is GNU using "--version" did not match "(GNU assembler)|(GCC)|(Free Software Foundation)":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf--version\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is AppleClang using "--version" did not match "(Apple (clang|LLVM) version)":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf--version\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is Clang using "--version" did not match "(clang version)":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf--version\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is ARMClang using "--version" did not match "armclang":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf--version\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is OrangeC using "--version" did not match "occ \\(OrangeC\\) Version":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf--version\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is HP using "-V" did not match "HP C":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8004 :\xa1\xb0/V\xa1\xb1\xd0\xe8Ҫ\xb2\xce\xca\xfd
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is Intel using "--version" did not match "(ICC)":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf--version\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is IntelLLVM using "--version" did not match "(Intel[^
      ]+oneAPI)":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf--version\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is SunPro using "-V" did not match "Sun C":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8004 :\xa1\xb0/V\xa1\xb1\xd0\xe8Ҫ\xb2\xce\xca\xfd
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1250 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is XL using "-qversion" did not match "XL C":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
      cl: \xc3\xfc\xc1\xee\xd0\xd0 warning D9002 :\xba\xf6\xc2\xd4δ֪ѡ\xcf-qversion\xa1\xb1
      cl: \xc3\xfc\xc1\xee\xd0\xd0 error D8003 :ȱ\xc9\xd9Դ\xceļ\xfe\xc3\xfb
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:1237 (message)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineASMCompiler.cmake:135 (CMAKE_DETERMINE_COMPILER_ID_VENDOR)"
      - "D:/dev/lpng1648/CMakeLists.txt:28 (project)"
    message: |
      Checking whether the ASM compiler is MSVC using "-?" matched "Microsoft":
      \xd3\xc3\xd3\xda x64 \xb5\xc4 Microsoft (R) C/C++ \xd3Ż\xaf\xb1\xe0\xd2\xeb\xc6\xf7 19.44.35211 \xb0\xe6
      \xb0\xe6Ȩ\xcb\xf9\xd3\xd0(C) Microsoft Corporation\xa1\xa3\xb1\xa3\xc1\xf4\xcb\xf9\xd3\xd0Ȩ\xc0\xfb\xa1\xa3
      
                   C/C++ \xb1\xe0\xd2\xeb\xc6\xf7ѡ\xcf\xee
      
      
                     -\xd3Ż\xaf-
      
      /O1 \xd7\xee\xb4\xf3\xd3Ż\xaf(\xd3\xc5ѡ\xbfռ\xe4)                  /O2 \xd7\xee\xb4\xf3\xd3Ż\xaf(\xd3\xc5ѡ\xcbٶ\xc8)
      /Ob<n> \xc4\xda\xc1\xaa\xc0\xa9չ(Ĭ\xc8\xcf n=0)               /Od \xbd\xfb\xd3\xc3\xd3Ż\xaf(Ĭ\xc8\xcf)
      /Og \xc6\xf4\xd3\xc3ȫ\xbe\xd6\xd3Ż\xaf                        /Oi[-] \xc6\xf4\xd3\xc3\xc4ڲ\xbf\xba\xaf\xca\xfd
      /Os \xd3\xc5ѡ\xb4\xfa\xc2\xeb\xbfռ\xe4                        /Ot \xd3\xc5ѡ\xb4\xfa\xc2\xeb\xcbٶ\xc8
      /Ox \xd3Ż\xaf(\xd3\xc5ѡ\xcbٶ\xc8)                      
      /favor:<blend|AMD64|INTEL64|ATOM> ѡ\xd4\xf1\xd3Ż\xaf\xcb\xf9\xd5\xeb\xb6ԵĴ\xa6\xc0\xed\xc6\xf7\xa3\xacΪ\xd2\xd4\xcf\xc2ֵ֮һ:
         blend - \xd5\xeb\xb6Լ\xb8\xd6ֲ\xbbͬ x64 \xb4\xa6\xc0\xed\xc6\xf7\xb5\xc4\xd3Ż\xaf\xd7\xe9\xba\xcf
         AMD64 - 64 λ AMD \xb4\xa6\xc0\xed\xc6\xf7
         INTEL64 - Intel(R)64 \xbcܹ\xb9\xb4\xa6\xc0\xed\xc6\xf7
         ATOM - Intel(R) Atom(TM) \xb4\xa6\xc0\xed\xc6\xf7
      
                     -\xb4\xfa\xc2\xeb\xc9\xfa\xb3\xc9-
      
      /Gu[-] ȷ\xb1\xa3 distinct \xba\xaf\xca\xfd\xbe\xdf\xd3з\xc7\xd6ظ\xb4\xb5\xd8ַ /Gw[-] \xb7ָ\xf4\xc1\xb4\xbd\xd3\xc6\xf7\xb5\xc4ȫ\xbeֱ\xe4\xc1\xbf
      /GF \xc6\xf4\xd3\xc3ֻ\xb6\xc1\xd7ַ\xfb\xb4\xae\xb3\xd8                    /Gy[-] \xb7ָ\xf4\xc1\xb4\xbd\xd3\xc6\xf7\xba\xaf\xca\xfd
      /GS[-] \xc6\xf4\xd3ð\xb2ȫ\xbc\xec\xb2\xe9                     /GR[-] \xc6\xf4\xd3\xc3 C++ RTTI
      /guard:cf[-] \xc6\xf4\xd3\xc3 CFG (\xbf\xd8\xd6\xc6\xc1\xf7\xb1\xa3\xbb\xa4)      /guard:ehcont[-] \xc6\xf4\xd3\xc3 EH \xbc\xcc\xd0\xf8Ԫ\xca\xfd\xbe\xdd(CET)
      /EHs \xc6\xf4\xd3\xc3 C++ EH (û\xd3\xd0 SEH \xd2쳣)        /EHa \xc6\xf4\xd3\xc3 C++ EH (w/ SEH \xd2쳣)
      /EHc \xcdⲿ "C" Ĭ\xc8\xcfΪ nothrow            /EHr ʼ\xd6\xd5\xc9\xfa\xb3\xc9 noexcept \xd4\xcb\xd0\xd0ʱ\xd6\xd5ֹ\xbc\xec\xb2\xe9
      /fp:<contract|except[-]|fast|precise|strict> ѡ\xd4񸡵\xe3ģ\xd0\xcd:
         Э\xb6\xa8 - \xc9\xfa\xb3ɴ\xfa\xc2\xebʱ\xbf\xbc\xc2Ǹ\xa1\xb5\xe3\xca\xd5\xcb\xf5
         except[-] - \xd4\xda\xc9\xfa\xb3ɴ\xfa\xc2\xebʱ\xbf\xbc\xc2Ǹ\xa1\xb5\xe3\xd2쳣
         fast - "fast" \xb8\xa1\xb5\xe3ģ\xd0ͣ\xbb\xbd\xe1\xb9\xfb\xbf\xc9Ԥ\xb2\xe2\xd0ԱȽϵ\xcd
         precise - "precise" \xb8\xa1\xb5\xe3ģ\xd0ͣ\xbb\xbd\xe1\xb9\xfb\xbf\xc9Ԥ\xb2\xe2
         strict - "strict" \xb8\xa1\xb5\xe3ģ\xd0\xcd(\xd2\xe2ζ\xd7\xc5 /fp:except)
      \xbc\xb4ʹʹ\xd3\xc3 /fp:except\xa3\xac/Qfast_transcendentals Ҳ\xc9\xfa\xb3\xc9\xc4\xda\xc1\xaa\xc4ڲ\xbf FP
      /Qspectre[-] \xb6\xd4 CVE 2017-5753 \xc6\xf4\xd3û\xba\xbd\xe2\xb4\xebʩ
      /Qpar[-] \xc6\xf4\xd3ò\xa2\xd0д\xfa\xc2\xeb\xc9\xfa\xb3\xc9               
      /Qpar-report:1 \xd7Զ\xaf\xb2\xa2\xd0л\xaf\xd5\xef\xb6ϣ\xbbָʾ\xd2Ѳ\xa2\xd0л\xafѭ\xbb\xb7
      /Qpar-report:2 \xd7Զ\xaf\xb2\xa2\xd0л\xaf\xd5\xef\xb6ϣ\xbbָʾδ\xb2\xa2\xd0л\xafѭ\xbb\xb7
      /Qvec-report:1 \xd7Զ\xaf\xcf\xf2\xc1\xbf\xbb\xaf\xd5\xef\xb6ϣ\xbbָʾ\xd2\xd1\xcf\xf2\xc1\xbf\xbb\xafѭ\xbb\xb7
      /Qvec-report:2 \xd7Զ\xaf\xcf\xf2\xc1\xbf\xbb\xaf\xd5\xef\xb6ϣ\xbbָʾδ\xcf\xf2\xc1\xbf\xbb\xafѭ\xbb\xb7
      /GL[-] \xc6\xf4\xd3\xc3\xc1\xb4\xbd\xd3ʱ\xb4\xfa\xc2\xeb\xc9\xfa\xb3\xc9               
      /volatile:<iso|ms> ѡ\xd4\xf1\xbfɱ\xe4ģ\xd0\xcd:
         iso - Acquire/release \xd3\xef\xd2\xe5\xb6Կɱ\xe4\xb7\xc3\xceʲ\xbbһ\xb6\xa8\xd3\xd0Ч
         ms - Acquire/release \xd3\xef\xd2\xe5\xb6Կɱ\xe4\xb7\xc3\xce\xcaһ\xb6\xa8\xd3\xd0Ч
      /GA Ϊ Windows Ӧ\xd3ó\xcc\xd0\xf2\xbd\xf8\xd0\xd0\xd3Ż\xaf         /Ge \xb6\xd4\xcb\xf9\xd3к\xaf\xca\xfdǿ\xd6ƶ\xd1ջ\xbc\xec\xb2\xe9
      /Gs[num] \xbf\xd8\xd6ƶ\xd1ջ\xbc\xec\xb2\xe9\xb5\xf7\xd3\xc3               /Gh \xc6\xf4\xd3\xc3 _penter \xba\xaf\xca\xfd\xb5\xf7\xd3\xc3
      /GH \xc6\xf4\xd3\xc3 _pexit \xba\xaf\xca\xfd\xb5\xf7\xd3\xc3                /GT \xc9\xfa\xb3\xc9\xcf˳̰\xb2ȫ TLS \xb7\xc3\xce\xca
      /RTC1 \xc6\xf4\xd3ÿ\xec\xcbټ\xec\xb2\xe9(/RTCsu)              /RTCc ת\xbb\xbbΪ\xbd\xcfС\xb5\xc4\xc0\xe0\xd0ͼ\xec\xb2\xe9
      /RTCs \xb6\xd1ջ֡\xd4\xcb\xd0\xd0ʱ\xbc\xec\xb2\xe9                  /RTCu δ\xb3\xf5ʼ\xbb\xaf\xb5ľֲ\xbf\xd3÷\xa8\xbc\xec\xb2\xe9
      /clr[\xa3\xbaoption] \xb1\xe0\xd2빫\xb9\xb2\xd3\xef\xd1\xd4\xd4\xcb\xd0\xd0ʱ\xa3\xac\xc6\xe4\xd6\xd0ѡ\xcf\xeeΪ\xa3\xba
          pure \xa3\xba \xc9\xfa\xb3ɽ\xf6 IL \xb5\xc4\xca\xe4\xb3\xf6\xceļ\xfe (û\xd3б\xbe\xbb\xfa\xbf\xc9ִ\xd0д\xfa\xc2\xeb)
          \xb0\xb2ȫ \xa3\xba \xc9\xfa\xb3ɽ\xf6 IL \xbf\xc9\xd1\xe9֤\xb5\xc4\xca\xe4\xb3\xf6\xceļ\xfe
          netcore \xa3\xba \xc9\xfa\xb3\xc9\xc3\xe6\xcf\xf2 .NET Core \xd4\xcb\xd0\xd0ʱ\xb5ĳ\xcc\xd0\xf2\xbc\xaf
          noAssembly \xa3\xba \xb2\xbb\xc9\xfa\xb3ɳ\xcc\xd0\xf2\xbc\xaf
          nostdlib \xa3\xba \xcb\xd1\xcb\xf7\xb3\xcc\xd0\xf2\xbc\xafʱ\xba\xf6\xc2\xd4ϵͳ .NET Framework Ŀ¼
          nostdimport \xa3\xba \xb2\xbb\xd2\xfeʽ\xb5\xbc\xc8\xeb\xc8κα\xd8\xd0\xe8\xb5ĳ\xcc\xd0\xf2\xbc\xaf
          initialAppDomain \xa3\xba \xc6\xf4\xd3\xc3 Visual C++ 2002 \xb5ĳ\xf5ʼ AppDomain \xd0\xd0Ϊ
          implicitKeepAlive- \xa3\xba \xb9ر\xd5 System\xa3\xba\xa3\xbaGC\xa3\xba\xa3\xbaKeepAlive \xb5\xc4\xd2\xfeʽ\xb7\xa2\xb3\xf6(\xb4\xcb)
          char_t- \xa3\xba \xb9رն\xd4char8_t\xa1\xa2char16_t\xba\xcdchar32_t\xb5\xc4Ԫ\xca\xfd\xbe\xdd֧\xb3\xd6
          ECMAParamArray \xa3\xba \xb6Բ\xce\xca\xfd\xca\xfd\xd7\xe9\xb5\xc4\xd6\xd8\xd4\xd8ʹ\xd3\xc3 ECMA-372/372/14.6 \xd6\xd0ָ\xb6\xa8\xb5Ĺ\xe6\xd4\xf2 (/clr)
          ECMAParamArray- ʹ\xd3ô\xf8\xd3в\xce\xca\xfd\xca\xfd\xd7\xe9\xb5\xc4\xd0¹\xe6\xd4\xf2\xa3\xac(/clr\xa3\xbanetcore)
      /fsanitize=\xbd\xe2\xbe\xf6\xc6\xf4\xd3õ\xd8ַ\xcf\xfb\xb6\xbe\xb4\xfa\xc2\xeb\xc9\xfa\xb3\xc9     
      /homeparams ǿ\xd6ƽ\xab\xb4\xab\xc8\xeb\xbcĴ\xe6\xc6\xf7\xb5Ĳ\xce\xca\xfdд\xc8뵽\xb6\xd1ջ\xd6\xd0
      /GZ \xc6\xf4\xd3ö\xd1ջ\xbc\xec\xb2\xe9(/RTCs)                 /Gv __vectorcall \xb5\xf7\xd3\xc3Լ\xb6\xa8
      (Ԥ\xc0\xc0\xb0\xe6)/dynamicdeopt \xc6\xf4\xd3ö\xaf̬\xb5\xf7\xcaԣ\xbb\xb7\xc5\xd6\xc3ȡ\xcf\xfb\xd3Ż\xaf\xb5Ķϵ㣬\xb2\xa2ͨ\xb9\xfd\xb0\xb4\xd0躯\xca\xfdȡ\xcf\xfb\xd3Ż\xaf\xcb\xe6ʱ\xcb\xe6\xb5ص\xa5\xb2\xbdִ\xd0\xd0
      (Ԥ\xc0\xc0) /dynamicdeopt:suffix <suffix> \xd3\xc3\xd3\xdaȥ\xd3Ż\xaf\xca\xe4\xb3\xf6\xb5\xc4\xceļ\xfe\xc0\xa9չ\xc3\xfb\xba\xf3׺(Ĭ\xc8\xcfֵ: .alt)
      (Ԥ\xc0\xc0) /dynamicdeopt:sync \xd4\xda\xc9\xfa\xb3\xc9\xd3Ż\xaf\xca\xe4\xb3\xf6\xba\xf3\xc9\xfa\xb3\xc9ȥ\xd3Ż\xaf\xca\xe4\xb3\xf6\xa3\xac\xb6\xf8\xb2\xbb\xcaǲ\xa2\xd0\xd0\xc9\xfa\xb3\xc9
      /arch:<SSE2|SSE4.2|AVX|AVX2|AVX512|AVX10.x> \xd7\xee\xb5\xcd CPU \xcc\xe5ϵ\xbdṹҪ\xc7\xf3\xa3\xac\xd2\xd4\xcf\xc2֮һ:
         SSE2 - (Ĭ\xc8\xcf)\xd4\xca\xd0\xedʹ\xd3\xc3ͨ\xb9\xfd֧\xb3\xd6 SSE2 \xb5\xc4 CPU \xccṩ\xb5\xc4ָ\xc1\xee
         SSE4.2 - \xd4\xca\xd0\xedʹ\xd3\xc3ͨ\xb9\xfd֧\xb3\xd6 SSE4.2 \xb5\xc4 CPU \xccṩ\xb5\xc4ָ\xc1\xee
         AVX - \xd4\xca\xd0\xedʹ\xd3\xc3ͨ\xb9\xfd֧\xb3\xd6 AVX \xb5\xc4 CPU \xccṩ\xb5\xc4ָ\xc1\xee
         AVX2 - \xd4\xca\xd0\xedʹ\xd3\xc3ͨ\xb9\xfd֧\xb3\xd6 AVX2 \xb5\xc4 CPU \xccṩ\xb5\xc4ָ\xc1\xee
         AVX512 - \xd4\xca\xd0\xedʹ\xd3\xc3ͨ\xb9\xfd֧\xb3\xd6 AVX-512 \xb5\xc4 CPU \xccṩ\xb5\xc4ָ\xc1\xee
         AVX - \xd4\xca\xd0\xedʹ\xd3\xc3ͨ\xb9\xfd֧\xb3\xd6 AVX10.x \xb5\xc4 CPU \xccṩ\xb5\xc4ָ\xc1x \xb5\xc4\xd3\xd0ЧֵΪ 1
      /QIntel-jcc-erratum Ϊ Intel JCC Erratum \xc6\xf4\xd3û\xba\xbd\xe2\xb4\xebʩ
      /Qspectre-load \xb6Լ\xd3\xd4\xd8\xc4ڴ\xe6\xb5\xc4\xcb\xf9\xd3\xd0ָ\xc1\xee\xc6\xf4\xd3\xc3 spectre \xbb\xba\xbd\xe2\xb4\xebʩ
      /Qspectre-load-cf \xb6Լ\xd3\xd4\xd8\xc4ڴ\xe6\xb5\xc4\xcb\xf9\xd3п\xd8\xd6\xc6\xc1\xf7ָ\xc1\xee\xc6\xf4\xd3\xc3 spectre \xbb\xba\xbd\xe2\xb4\xebʩ
      /Qspectre-jmp[-] Ϊ\xce\xde\xcc\xf5\xbc\xfe\xcc\xf8תָ\xc1\xee\xc6\xf4\xd3\xc3 Spectre \xbb\xba\xbd\xe2
      /fpcvt:<IA|BC> \xce޷\xfb\xba\xc5\xd5\xfb\xca\xfdת\xbb\xbb\xbc\xe6\xc8\xdd\xd0\xd4Ϊ FP
        IA - \xd3\xeb VCVTTSD2USI ָ\xc1\xee\xbc\xe6\xc8ݵĽ\xe1\xb9\xfb
        BC - \xd3\xeb VS2017 \xbc\xb0\xb8\xfc\xb5Ͱ汾\xb5ı\xe0\xd2\xeb\xc6\xf7\xbc\xe6\xc8ݵĽ\xe1\xb9\xfb
      /jumptablerdata \xd4\xda .rdata \xb2\xbf\xb7\xd6\xd6з\xc5\xd6\xc3 switch case \xd3\xef\xbe\xe4\xb5\xc4\xcc\xf8ת\xb1\xed
      /vlen=<256|512> Ϊ\xd7Զ\xaf\xb4\xfa\xc2\xeb\xc9\xfa\xb3\xc9ѡ\xd4\xf1 256 \xbb\xf2 512 \xb5\xc4ʸ\xc1\xbf\xb3\xa4\xb6\xc8
      /vlen \xb8\xf9\xbe\xdd /arch \xc9\xe8\xd6\xc3ѡ\xd4\xf1Ĭ\xc8\xcfʸ\xc1\xbf\xb3\xa4\xb6\xc8   
      
                     -\xca\xe4\xb3\xf6\xceļ\xfe-
      
      /Fa[file] \xc3\xfc\xc3\xfb\xb3\xcc\xd0\xf2\xbc\xaf\xc1б\xed\xceļ\xfe            /FA[scu] \xc5\xe4\xd6ó\xcc\xd0\xf2\xbc\xaf\xc1б\xed
      /Fd[file] \xc3\xfc\xc3\xfb .PDB \xceļ\xfe                /Fe<file> \xc3\xfc\xc3\xfb\xbf\xc9ִ\xd0\xd0\xceļ\xfe
      /Fm[file] \xc3\xfc\xc3\xfbӳ\xc9\xe4\xceļ\xfe                  /Fo<file> \xc3\xfc\xc3\xfb\xb6\xd4\xcf\xf3\xceļ\xfe
      /Fp<file> \xc3\xfc\xc3\xfbԤ\xb1\xe0\xd2\xebͷ\xceļ\xfe              /Fr[file] \xc3\xfc\xc3\xfbԴ\xe4\xaf\xc0\xc0\xc6\xf7\xceļ\xfe
      /FR[file] \xc3\xfc\xc3\xfb\xc0\xa9չ .SBR \xceļ\xfe            /Fi[file] \xc3\xfc\xc3\xfbԤ\xb4\xa6\xc0\xed\xb5\xc4\xceļ\xfe
      /Fd: <file> \xc3\xfc\xc3\xfb .PDB \xceļ\xfe              /Fe: <file> \xc3\xfc\xc3\xfb\xbf\xc9ִ\xd0\xd0\xceļ\xfe
      /Fm: <file> \xc3\xfc\xc3\xfbӳ\xc9\xe4\xceļ\xfe                /Fo: <file> \xc3\xfc\xc3\xfb\xb6\xd4\xcf\xf3\xceļ\xfe
      /Fp: <file> \xc3\xfc\xc3\xfb .PCH \xceļ\xfe              /FR: <file> \xc3\xfc\xc3\xfb\xc0\xa9չ .SBR \xceļ\xfe
      /Fi: <file> \xc3\xfc\xc3\xfbԤ\xb4\xa6\xc0\xed\xb5\xc4\xceļ\xfe            /Ft<dir> Ϊ #import \xc9\xfa\xb3ɵ\xc4ͷ\xceļ\xfe\xb5ĵ\xd8ַ
      /doc[file] \xb4\xa6\xc0\xed XML \xceĵ\xb5ע\xcaͣ\xac\xb2\xa2\xbf\xc9ѡ\xd4\xf1\xc3\xfc\xc3\xfb .xdc \xceļ\xfe
      
                     -Ԥ\xb4\xa6\xc0\xed\xc6\xf7-
      
      /AI<dir> \xcc\xed\xbcӵ\xbd\xb3\xcc\xd0\xf2\xbc\xaf\xcb\xd1\xcb\xf7·\xbe\xb6           /FU<file> \xb5\xbc\xc8\xeb .NET \xb3\xcc\xd0\xf2\xbc\xaf/ģ\xbf\xe9
      /FU:asFriend<file> \xbd\xab .NET \xb3\xcc\xd0\xf2\xbc\xaf/ģ\xbf鵼\xc8\xebΪ\xd3\xd1Ԫ
      /C \xb2\xbb\xb3\xe9\xb3\xf6ע\xca\xcd                           /D<name>{=|#}<text> \xb6\xa8\xd2\xe5\xba\xea
      /E Ԥ\xb4\xa6\xc0\xed\xb5\xbd stdout                      /EP Ԥ\xb4\xa6\xc0\xed\xb5\xbd stdout\xa3\xac\xce\xde\xd0к\xc5
      /P Ԥ\xb4\xa6\xc0\xed\xb5\xbd\xceļ\xfe                         /Fx \xbd\xab\xb2\xe5\xc8\xeb\xb5Ĵ\xfa\xc2\xeb\xbaϲ\xa2\xb5\xbd\xceļ\xfe\xd6\xd0
      /FI<file> \xc3\xfc\xc3\xfbǿ\xd6ư\xfc\xba\xac\xceļ\xfe              /U<name> \xd2Ƴ\xfdԤ\xb6\xa8\xd2\xe5\xb5ĺ\xea
      /u \xd2Ƴ\xfd\xcb\xf9\xd3\xd0Ԥ\xb6\xa8\xd2\xe5\xb5ĺ\xea                   /I<dir> \xcc\xed\xbcӵ\xbd\xb0\xfc\xba\xac\xcb\xd1\xcb\xf7·\xbe\xb6
      /X \xba\xf6\xc2ԡ\xb0\xb1\xea׼λ\xd6á\xb1                     /PH \xd4\xdaԤ\xb4\xa6\xc0\xedʱ\xc9\xfa\xb3\xc9 #pragma file_hash
      /PD \xb4\xf2ӡ\xcb\xf9\xd3к궨\xd2\xe5                      
      
                      -\xd3\xef\xd1\xd4-
      
      /std:<c++14|c++17|c++20|c++latest> C++ \xb1\xea׼\xb0\xe6
          c++14 - ISO/IEC 14882:2014 (Ĭ\xc8\xcfֵ)
          c++17 - ISO/IEC 14882:2017
          c++20 - ISO/IEC 14882:2020
          c++latest - \xd7\xee\xd0²ݰ\xb8\xb1\xea׼(\xb9\xa6\xc4ܼ\xaf\xbf\xc9\xc4ܸ\xfc\xb8\xc4)
      /std:<c11|c17|clatest> C \xb1\xea׼\xb0汾
         c11 - ISO/IEC 9899:2011
         c17 - ISO/IEC 9899:2018
         clatest - \xd7\xee\xd0²ݸ\xe5\xb1\xea׼(\xb9\xa6\xc4ܼ\xaf\xbf\xc9\xc4ܻ\xe1\xb8\xfc\xb8\xc4)
      /permissive[-] \xc6\xf4\xd3\xc3һЩ\xb2\xbb\xb7\xfb\xbaϸ\xf1ʽ\xb5Ĵ\xfa\xc2\xeb\xbd\xf8\xd0б\xe0\xd2\xeb
                    (\xb9\xa6\xc4ܼ\xaf\xbf\xc9\xc4ܻ\xe1\xb8\xfc\xb8\xc4)(\xd4\xda C++20 \xbc\xb0\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8Ϲر\xd5)
      /Za \xbd\xfb\xd3\xc3\xc0\xa9չ(\xb2\xbb\xbd\xa8\xd2\xe9\xd3\xc3\xd3\xda C++)            /ZW \xc6\xf4\xd3\xc3 WinRT \xd3\xef\xd1\xd4\xc0\xa9չ
      /Zs ֻ\xbd\xf8\xd0\xd0\xd3﷨\xbc\xec\xb2\xe9                      
      /await \xc6\xf4\xd3ÿɻָ\xb4\xba\xaf\xca\xfd\xc0\xa9չ
      /await:strict \xc6\xf4\xd3\xc3ʹ\xd3\xc3\xd4\xe7\xc6\xda\xd3\xef\xd1԰汾\xb5ı\xea׼ C++20 Эͬ\xc0\xfd\xb3\xcc֧\xb3\xd6
      /constexpr:depth<N>     constexpr \xc6\xc0\xb9\xc0\xb5ĵݹ\xe9\xc9\xee\xb6\xc8\xcf\xde\xd6\xc6(Ĭ\xc8\xcfֵ: 512)
      /constexpr:backtrace<N> \xd4\xda\xd5\xef\xb6\xcf\xd6\xd0\xcf\xd4ʾ N constexpr \xc6\xc0\xb9\xc0(Ĭ\xc8\xcfֵ: 10)
      /constexpr:steps<N>     \xd4\xda N \xb8\xf6\xb2\xbd\xd6\xe8\xba\xf3\xd6\xd5ֹ constexpr \xc6\xc0\xb9\xc0(Ĭ\xc8\xcfֵ: 1048576)
      /Zi \xc6\xf4\xd3õ\xf7\xca\xd4\xd0\xc5Ϣ                        /Z7 \xc6\xf4\xd3þ\xc9ʽ\xb5\xf7\xca\xd4\xd0\xc5Ϣ
      /Zo[-] Ϊ\xd3Ż\xaf\xb5Ĵ\xfa\xc2\xeb\xc9\xfa\xb3ɸ\xfc\xb7ḻ\xb5ĵ\xf7\xca\xd4\xd0\xc5Ϣ(Ĭ\xc8Ͽ\xaa\xc6\xf4)
      /ZH:[MD5|SHA1|SHA_256] \xb9\xfeϣ\xcb㷨\xa3\xac\xd3\xc3\xd3\xda\xd4ڵ\xf7\xca\xd4\xd0\xc5Ϣ\xd6м\xc6\xcb\xe3\xceļ\xfeУ\xd1\xe9\xba\xcd(Ĭ\xc8\xcfֵ: SHA_256)
      /Zp[n] \xd4\xda n \xd7ֽڱ߽\xe7\xc9ϰ\xfcװ\xbdṹ          /Zl ʡ\xc2\xd4 .OBJ \xd6е\xc4Ĭ\xc8Ͽ\xe2\xc3\xfb
      /vd{0|1|2} \xbd\xfb\xd3\xc3/\xc6\xf4\xd3\xc3 vtordisp           /vm<x> ָ\xcf\xf2\xb3\xc9Ա\xb5\xc4ָ\xd5\xeb\xc0\xe0\xd0\xcd
      /Zc:arg1[,arg2] \xd3\xef\xd1\xd4һ\xd6\xc2\xd0ԣ\xac\xd5\xe2\xc0\xef\xb5Ĳ\xce\xca\xfd\xbf\xc9\xd2\xd4\xca\xc7:
       forScope[-]      \xb6Է\xb6Χ\xb9\xe6\xd4\xf2ǿ\xd6\xc6ʹ\xd3ñ\xea׼ C++
       wchar_t[-]      wchar_t \xcaǱ\xbe\xbb\xfa\xc0\xe0\xd0ͣ\xac\xb2\xbb\xca\xc7 typedef
       auto[-]               \xb6\xd4 auto ǿ\xd6\xc6ʹ\xd3\xc3\xd0µı\xea׼ C++ \xba\xac\xd2\xe5
       trigraphs[-]          \xc6\xf4\xd3\xc3\xc8\xfdԪ\xd7\xe6(Ĭ\xc8Ϲر\xd5)
       rvalueCast[-]     ǿ\xd6\xc6ʵʩ\xb1\xea׼ C++ \xcf\xd4ʽ\xc0\xe0\xd0\xcdת\xbb\xbb\xb9\xe6\xd4\xf2
                             (\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8\xcf\xc6\xf4\xd3ã\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       strictStrings[-]      \xbd\xfb\xd3\xc3\xd7ַ\xfb\xb4\xae\xceı\xbe\xb5\xbd [char|wchar_t]*
                             ת\xbb\xbb(\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8\xcf\xc6\xf4\xd3ã\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       implicitNoexcept[-]  \xd4ڱ\xd8\xd0\xe8\xb5ĺ\xaf\xca\xfd\xc9\xcf\xc6\xf4\xd3\xc3\xd2\xfeʽ noexcept
       threadSafeInit[-]   \xc6\xf4\xd3\xc3\xcf̰߳\xb2ȫ\xb5ı\xbe\xb5ؾ\xb2̬\xb3\xf5ʼ\xbb\xaf
       inline[-]             ɾ\xb3\xfdδ\xd2\xfd\xd3õĺ\xaf\xca\xfd\xbb\xf2\xca\xfd\xbeݣ\xac\xc8\xe7\xb9\xfb\xc6\xe4Ϊ
                             COMDAT \xbb\xf2\xbd\xf6\xbe\xdf\xd3\xd0\xc4ڲ\xbf\xc1\xb4\xbd\xd3(Ĭ\xc8\xcf\xc7\xe9\xbf\xf6\xcf¹ر\xd5)
       sizedDealloc[-]       \xc6\xf4\xd3\xc3 C++14 ȫ\xbeִ\xf3С\xb5Ľ\xe2\xb3\xfd\xb7\xd6\xc5\xe4
                             \xba\xaf\xca\xfd(Ĭ\xc8\xcf\xc6\xf4\xd3\xc3)
       throwingNew[-]        \xbc\xd9\xc9\xe8\xd4\xcb\xcb\xe3\xb7\xfb new \xd4ڹ\xca\xd5\xcfʱ\xd2\xfd\xb7\xa2(Ĭ\xc8Ϲر\xd5)
       referenceBinding[-]   \xc1\xd9ʱ\xa3\xac\xb2\xbb\xbb\xe1\xb0󶨵\xbd\xb7ǳ\xa3\xc1\xbf
                             ֵ\xd2\xfd\xd3\xc3(Ĭ\xc8\xcf\xc7\xe9\xbf\xf6\xcf\xc2Ϊ C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xa3\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       twoPhase-             \xbd\xfb\xd3\xc3\xc1\xbd\xbd׶\xce\xc3\xfb\xb3Ʋ\xe9\xd5\xd2
       ternary[-]            \xb6\xd4\xcc\xf5\xbc\xfe\xd4\xcb\xcb\xe3\xb7\xfbǿ\xd6\xc6ִ\xd0\xd0 C++11 \xb9\xe6\xd4\xf2
                             (\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8\xcf\xc6\xf4\xd3ã\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       noexceptTypes[-]      ǿ\xd6\xc6ִ\xd0\xd0 C++17 noexcept \xb9\xe6\xd4\xf2(\xd4\xda C++17 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8Ͽ\xaa\xc6\xf4)
       alignedNew[-]         \xb6Զ\xaf̬\xb7\xd6\xc5\xe4\xb5Ķ\xd4\xcf\xf3\xc6\xf4\xd3\xc3 C++17 \xb6\xd4\xc6뷽ʽ(Ĭ\xc8Ͽ\xaa\xc6\xf4)
       hiddenFriend[-]       ǿ\xd6\xc6ִ\xd0б\xea׼ C++ \xd2\xfe\xb2\xd8\xd3\xd1Ԫ\xb9\xe6\xd4\xf2
                             (\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8\xcf\xc6\xf4\xd3ã\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       externC[-]            \xb6\xd4 'extern "C"' \xba\xaf\xca\xfdǿ\xd6\xc6ִ\xd0б\xea׼ C++ \xb9\xe6\xd4\xf2
                             (\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8\xcf\xc6\xf4\xd3ã\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       lambda[-]             ʹ\xd3ø\xfc\xd0µ\xc4 lambda \xb4\xa6\xc0\xed\xc6\xf7\xccṩ\xb8\xfc\xbaõ\xc4 lambda ֧\xb3\xd6
                             (\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8\xcf\xc6\xf4\xd3ã\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       tlsGuards[-]          \xc9\xfa\xb3\xc9 TLS \xb1\xe4\xc1\xbf\xb3\xf5ʼ\xbb\xaf\xb5\xc4\xd4\xcb\xd0\xd0ʱ\xbc\xec\xb2\xe9(Ĭ\xc8\xcf\xc7\xe9\xbf\xf6\xcf\xc2\xc6\xf4\xd3\xc3)
       zeroSizeArrayNew[-]   \xd5\xeb\xb6Դ\xf3СΪ\xc1\xe3\xb5Ķ\xd4\xcf\xf3\xca\xfd\xd7\xe9\xb5ĵ\xf7\xd3ö\xd4\xcf\xf3 new/delete (Ĭ\xc8\xcf\xc6\xf4\xd3\xc3)
       static_assert[-]      \xd1ϸ\xf1\xb4\xa6\xc0\xed "static_assert" (\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8ϴ\xa6\xd3ڴ\xf2\xbf\xaa״̬\xa3\xac
                             \xd3\xc9 /permissive- \xd2\xfe\xba\xac)
       gotoScope[-]          \xce޷\xa8\xcc\xf8\xb9\xfd\xb1\xe4\xc1\xbf\xb5ĳ\xf5ʼ\xbb\xaf(\xd3\xc9 /permissive- \xb0\xb5ʾ)
       templateScope[-]      ǿ\xd6\xc6ִ\xd0б\xea׼ C++ ģ\xb0\xe5\xb2\xce\xca\xfd\xd2\xf5Ӱ\xb9\xe6\xd4\xf2
       enumTypes[-]          \xc6\xf4\xd3ñ\xea׼ C++ \xbb\xf9\xb4\xa1ö\xbe\xd9\xc0\xe0\xd0\xcd(Ĭ\xc8\xcf\xc7\xe9\xbf\xf6\xcf¹ر\xd5)
       checkGwOdr[-]         ǿ\xd6\xc6ʵʩ\xb1\xea׼ C++ һ\xb8\xf6\xb6\xa8\xd2\xe5\xb9\xe6\xd4\xf2Υ\xb7\xb4
                             \xc6\xf4\xd3\xc3 /Gw ʱ(Ĭ\xc8Ϲر\xd5)
       nrvo[-]               \xc6\xf4\xd3ÿ\xc9ѡ\xb8\xb4\xd6ƺ\xcd\xd2ƶ\xaf\xcf\xfb\xb3\xfd(\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8Ͽ\xaa\xc6\xf4\xa3\xac
                             \xd3\xc9 /permissive \xbb\xf2 /O2 \xd2\xfe\xba\xac)
       __STDC__\xd4\xda C \xd6н\xab __STDC__ \xb6\xa8\xd2\xe5Ϊ 1
       __cplusplus[-]        __cplusplus \xba걨\xb8\xe6֧\xb3ֵ\xc4 C++ \xb1\xea׼(Ĭ\xc8Ϲر\xd5)
       char8_t[-]            \xbd\xab C++20 \xb1\xbe\xbb\xfa`u8`\xceı\xbe֧\xb3\xd6\xc6\xf4\xd3\xc3Ϊ`const char8_t`
                             (Ĭ\xc8\xcf\xc7\xe9\xbf\xf6\xcf\xc2Ϊ C++20 \xbb\xf2\xb8\xfc\xb8߰汾)
       externConstexpr[-]    Ϊ C++ \xd6е\xc4 constexpr \xb1\xe4\xc1\xbf\xc6\xf4\xd3\xc3\xcdⲿ\xc1\xb4\xbd\xd3
                             (\xd4\xda C++20 \xbb\xf2\xb8\xfc\xb8߰汾\xd6\xd0Ĭ\xc8\xcf\xc6\xf4\xd3ã\xac\xd3\xc9 /permissive-\xd2\xfe\xba\xac)
       preprocessor[-]       \xd4\xda C/C++ \xd6\xd0\xc6\xf4\xd3ñ\xea׼һ\xd6\xc2\xd0\xd4Ԥ\xb4\xa6\xc0\xed\xc6\xf7
                             (Ĭ\xc8\xcf\xc7\xe9\xbf\xf6\xcf\xc2Ϊ C11 \xbb\xf2\xb8\xfc\xb8߰汾)
      /ZI \xc6\xf4\xd3á\xb0\xb1༭\xb2\xa2\xbc\xcc\xd0\xf8\xa1\xb1\xb5\xf7\xca\xd4\xd0\xc5Ϣ          /openmp \xc6\xf4\xd3\xc3 OpenMP 2.0 \xd3\xef\xd1\xd4\xc0\xa9չ
      /openmp:experimental \xbb\xe1\xc6\xf4\xd3\xc3 OpenMP 2.0 \xd3\xef\xd1\xd4\xc0\xa9չ\xb2\xa2ѡ\xd4\xf1 OpenMP 3.0+ \xd3\xef\xd1\xd4\xc0\xa9չ
      ʹ\xd3\xc3 LLVM \xd4\xcb\xd0\xd0ʱ /openmp:llvm OpenMP \xd3\xef\xd1\xd4\xc0\xa9չ
      
                     - \xd4\xd3\xcf\xee -
      
      @<file> ѡ\xcf\xee\xcf\xecӦ\xceļ\xfe                    /?, /help \xb4\xf2ӡ\xb4˰\xef\xd6\xfa\xcf\xfbϢ
      /bigobj \xc9\xfa\xb3\xc9\xc0\xa9չ\xb5Ķ\xd4\xcf\xf3\xb8\xf1ʽ              /c ֻ\xb1\xe0\xd2룬\xb2\xbb\xc1\xb4\xbd\xd3
      /FC \xd5\xef\xb6\xcf\xd6\xd0ʹ\xd3\xc3\xcd\xea\xd5\xfb·\xbe\xb6\xc3\xfb                /H<num> \xd7\xee\xb4\xf3\xcdⲿ\xc3\xfb\xb3Ƴ\xa4\xb6\xc8
      /J Ĭ\xc8\xcf char \xc0\xe0\xd0\xcd\xca\xc7 unsigned            /MP[n] \xd7\xee\xb6\xe0ʹ\xd3á\xb0n\xa1\xb1\xb8\xf6\xbd\xf8\xb3̽\xf8\xd0б\xe0\xd2\xeb
      /nologo ȡ\xcf\xfb\xcf\xd4ʾ\xb0\xe6Ȩ\xd0\xc5Ϣ                /showIncludes \xcf\xd4ʾ\xb0\xfc\xba\xac\xceļ\xfe\xc3\xfb
      /Tc<source file> \xbd\xab\xceļ\xfe\xb1\xe0\xd2\xebΪ .c        /Tp<source file> \xbd\xab\xceļ\xfe\xb1\xe0\xd2\xebΪ .cpp
      /TC \xbd\xab\xcb\xf9\xd3\xd0\xceļ\xfe\xb1\xe0\xd2\xebΪ .c                 /TP \xbd\xab\xcb\xf9\xd3\xd0\xceļ\xfe\xb1\xe0\xd2\xebΪ .cpp
      /V<string> \xc9\xe8\xd6ð汾\xd7ַ\xfb\xb4\xae               /Yc[file] \xb4\xb4\xbd\xa8 .PCH \xceļ\xfe
      /Yd \xbd\xab\xb5\xf7\xca\xd4\xd0\xc5Ϣ\xb7\xc5\xd4\xdaÿ\xb8\xf6 .OBJ \xd6\xd0          /Yl[sym] Ϊ\xb5\xf7\xcaԿ\xe2\xb2\xe5\xc8\xeb .PCH \xd2\xfd\xd3\xc3
      /Yu[file] ʹ\xd3\xc3 .PCH \xceļ\xfe                /Y- \xbd\xfb\xd3\xc3\xcb\xf9\xd3\xd0 PCH ѡ\xcf\xee
      /Zm<n> \xd7\xee\xb4\xf3\xc4ڴ\xe6\xb7\xd6\xc5\xe4(Ĭ\xc8\xcfֵ\xb5İٷֱ\xc8)     /FS ǿ\xd6\xc6ʹ\xd3\xc3 MSPDBSRV.EXE
      /source-charset:<iana-name>|.nnnn \xbc\xafԴ\xd7ַ\xfb\xbc\xaf
      /execution-charset:<iana-name>|.nnnn \xbc\xafִ\xd0\xd0\xd7ַ\xfb\xbc\xaf
      /utf-8 \xbc\xafԴ\xba͵\xbd UTF-8 \xb5\xc4ִ\xd0\xd0\xd7ַ\xfb\xbc\xaf      
      /validate-charset[-] \xd1\xe9֤ UTF-8 \xceļ\xfe\xcaǷ\xf1ֻ\xd3кϷ\xa8\xd7ַ\xfb
      /fastfail[-] \xc6\xf4\xd3\xc3 fast-fail ģʽ        /JMC[-] \xc6\xf4\xd3ñ\xbe\xbb\xfa\xa1\xb0\xbd\xf6\xceҵĴ\xfa\xc2롱
      /presetPadding[-] \xbd\xab\xbb\xf9\xd3ڶ\xd1ջ\xb5\xc4\xc0\xe0\xc0\xe0\xd0͵\xc4\xcc\xee\xb3\xe4\xb3\xf5ʼ\xbb\xafΪ\xc1\xe3
      /volatileMetadata[-] \xb6\xd4\xd2\xd7ʧ\xb4洢\xc6\xf7\xb7\xc3\xce\xca\xc9\xfa\xb3\xc9Ԫ\xca\xfd\xbe\xdd
      /sourcelink [file] \xb0\xfc\xba\xacԴ\xc1\xb4\xbd\xd3\xd0\xc5Ϣ\xb5\xc4\xceļ\xfe 
      
                      -\xc1\xb4\xbd\xd3-
      
      /LD \xb4\xb4\xbd\xa8 .DLL                           /LDd \xb4\xb4\xbd\xa8 .DLL \xb5\xf7\xcaԿ\xe2
      /LN \xb4\xb4\xbd\xa8 .netmodule                     /F<num> \xc9\xe8\xd6ö\xd1ջ\xb4\xf3С
      /link [\xc1\xb4\xbd\xd3\xc6\xf7ѡ\xcf\xee\xbaͿ\xe2]                  /MD \xd3\xeb MSVCRT.LIB \xc1\xb4\xbd\xd3
      /MT \xd3\xeb LIBCMT.LIB \xc1\xb4\xbd\xd3                  /MDd \xd3\xeb MSVCRTD.LIB \xb5\xf7\xcaԿ\xe2\xc1\xb4\xbd\xd3
      /MTd \xd3\xeb LIBCMTD.LIB \xb5\xf7\xcaԿ\xe2\xc1\xb4\xbd\xd3          
      
                     -\xb4\xfa\xc2\xeb\xb7\xd6\xce\xf6-
      
      /analyze[-] \xc6\xf4\xd3ñ\xbe\xbb\xfa\xb7\xd6\xce\xf6                /analyze:quiet[-] û\xd3жԿ\xd8\xd6\xc6̨\xb5ľ\xaf\xb8\xe6
      /analyze:log<name> \xb6\xd4\xceļ\xfe\xb5ľ\xaf\xb8\xe6         /analyze:autolog Log to *.pftlog
      /analyze:autolog:ext<ext> Log to *.<ext>/analyze:autolog- \xce\xde\xc8\xd5־\xceļ\xfe
      /analyze:WX- \xbe\xaf\xb8治\xd1\xcf\xd6\xd8                 /analyze:stacksize<num> \xd7\xee\xb4\xf3\xb6\xd1ջ֡
      /analyze:max_paths<num> \xd7\xee\xb4\xf3·\xbe\xb6        /analyze:only Analyze, no code gen
      
                                    -\xd5\xef\xb6\xcf-
      
      /diagnostics:<args,...> \xbf\xd8\xd6\xc6\xd5\xef\xb6\xcf\xcf\xfbϢ\xb5ĸ\xf1ʽ:
                   \xb4\xabͳ\xd0\xcd - \xb1\xa3\xc1\xf4֮ǰ\xb5ĸ\xf1ʽ
                   \xc1\xd0[-] - \xb4\xf2ӡ\xc1\xd0\xd0\xc5Ϣ
                   \xcd\xd1\xd7ֺ\xc5[-] - \xb4\xf2ӡ\xc1к\xcdԴ\xb5\xc4ָʾ\xd0\xd0
      /Wall \xc6\xf4\xd3\xc3\xcb\xf9\xd3о\xaf\xb8\xe6                      /w   \xbd\xfb\xd3\xc3\xcb\xf9\xd3о\xaf\xb8\xe6
      /W<n> \xc9\xe8\xd6þ\xaf\xb8\xe6\xb5ȼ\xb6(Ĭ\xc8\xcf n=1)            
      /Wv:xx[.yy[.zzzzz]] \xbd\xfb\xd3\xc3\xd4\xda xx.yy.zzzzz \xb0汾\xba\xf3\xd2\xfd\xc8\xeb\xb5ľ\xaf\xb8湦\xc4\xdc
      /WX \xbd\xab\xbe\xaf\xb8\xe6\xca\xd3Ϊ\xb4\xed\xce\xf3                      /WL \xc6\xf4\xd3õ\xa5\xd0\xd0\xd5\xef\xb6\xcf
      /wd<n> \xbd\xfb\xd3þ\xaf\xb8\xe6 n                       /we<n> \xbd\xab\xbe\xaf\xb8\xe6 n \xca\xd3Ϊ\xb4\xed\xce\xf3
      /wo<n> \xb7\xa2\xb3\xf6һ\xb4ξ\xaf\xb8\xe6 n                   /w<l><n> Ϊ n \xc9\xe8\xd6þ\xaf\xb8\xe6\xb5ȼ\xb6 1-4
      /external:I <path>      - \xcdⲿ\xb1\xeaͷ\xb5\xc4λ\xd6\xc3
      /external:env:<var>    - \xcdⲿ\xb1\xeaͷλ\xd6õĻ\xb7\xbe\xb3\xb1\xe4\xc1\xbf
      /external:anglebrackets - \xbd\xab\xcb\xf9\xd3\xd0ͨ\xb9\xfd <> \xb0\xfc\xba\xac\xb5ı\xeaͷ\xca\xd3Ϊ\xcdⲿ
      /external:W<n>          - \xcdⲿ\xb1\xeaͷ\xb5ľ\xaf\xb8\xe6\xb5ȼ\xb6
      /external:templates[-]  - \xbf\xe7ģ\xb0\xe5ʵ\xc0\xfd\xbb\xaf\xc1\xb4\xc6\xc0\xb9\xc0\xbe\xaf\xb8漶\xb1\xf0
      /sdl ֧\xb3\xd6\xc6\xe4\xcb\xfb\xb0\xb2ȫ\xb9\xa6\xc4ܺ;\xaf\xb8\xe6             
      /ѡ\xcf\xee: \xd1ϸ\xf1\xce޷\xa8ʶ\xb1\xf0\xb5ı\xe0\xd2\xeb\xc6\xf7ѡ\xcf\xee\xca\xc7һ\xb8\xf6\xb4\xed\xce\xf3
      
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "D:/dev/lpng1648/CMakeLists.txt:359 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_LD_VERSION_SCRIPT"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-trjfok"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-trjfok"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "HAVE_LD_VERSION_SCRIPT"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-trjfok'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_3c220.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:12:59銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\cmTC_3c220.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_3c220.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_3c220.dir\\Debug\\cmTC_3c220.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_3c220.dir\\Debug\\cmTC_3c220.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_3c220.dir\\Debug\\cmTC_3c220.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_LD_VERSION_SCRIPT /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_3c220.dir\\Debug\\\\" /Fd"cmTC_3c220.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue  "-Wl,--version-script='D:/dev/BLPConverter/build/x64-release/libpng/conftest.map'" "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_LD_VERSION_SCRIPT /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_3c220.dir\\Debug\\\\" /Fd"cmTC_3c220.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue  "-Wl,--version-script='D:/dev/BLPConverter/build/x64-release/libpng/conftest.map'" "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\src.c"
        cl : 鍛戒护琛? error D8021: 鏃犳晥鐨勬暟鍊煎弬鏁扳€?Wl,--version-script='D:/dev/BLPConverter/build/x64-release/libpng/conftest.map'鈥?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\cmTC_3c220.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\cmTC_3c220.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\cmTC_3c220.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          cl : 鍛戒护琛? error D8021: 鏃犳晥鐨勬暟鍊煎弬鏁扳€?Wl,--version-script='D:/dev/BLPConverter/build/x64-release/libpng/conftest.map'鈥?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-trjfok\\cmTC_3c220.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.53
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "D:/dev/lpng1648/CMakeLists.txt:372 (check_c_source_compiles)"
    checks:
      - "Performing Test HAVE_SOLARIS_LD_VERSION_SCRIPT"
    directories:
      source: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-z0r509"
      binary: "D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-z0r509"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "HAVE_SOLARIS_LD_VERSION_SCRIPT"
      cached: true
      stdout: |
        Change Dir: 'D:/dev/BLPConverter/build/x64-release/CMakeFiles/CMakeScratch/TryCompile-z0r509'
        
        Run Build Command(s): "C:/Program Files (x86)/Microsoft Visual Studio/2022/BuildTools/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b43fa.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/24 19:13:00銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\cmTC_b43fa.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b43fa.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b43fa.dir\\Debug\\cmTC_b43fa.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_b43fa.dir\\Debug\\cmTC_b43fa.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_b43fa.dir\\Debug\\cmTC_b43fa.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_SOLARIS_LD_VERSION_SCRIPT /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_b43fa.dir\\Debug\\\\" /Fd"cmTC_b43fa.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue  -Wl,-M -Wl,D:/dev/BLPConverter/build/x64-release/libpng/conftest.map "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_SOLARIS_LD_VERSION_SCRIPT /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c17 /Fo"cmTC_b43fa.dir\\Debug\\\\" /Fd"cmTC_b43fa.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue  -Wl,-M -Wl,D:/dev/BLPConverter/build/x64-release/libpng/conftest.map "D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\src.c"
        cl : 鍛戒护琛? error D8021: 鏃犳晥鐨勬暟鍊煎弬鏁扳€?Wl,-M鈥?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\cmTC_b43fa.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\cmTC_b43fa.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\cmTC_b43fa.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          cl : 鍛戒护琛? error D8021: 鏃犳晥鐨勬暟鍊煎弬鏁扳€?Wl,-M鈥?[D:\\dev\\BLPConverter\\build\\x64-release\\CMakeFiles\\CMakeScratch\\TryCompile-z0r509\\cmTC_b43fa.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.60
        
      exitCode: 1
...
