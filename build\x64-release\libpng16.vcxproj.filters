﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\dev\lpng1648\png.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngerror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngget.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngpread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngrio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngrtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngrutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngtrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwrite.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\arm\arm_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\arm\filter_neon_intrinsics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\arm\palette_neon_intrinsics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\dev\BLPConverter\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{96D470C5-0D0F-39F2-8FB2-821C11899131}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
