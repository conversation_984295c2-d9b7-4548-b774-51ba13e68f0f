﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\dev\lpng1648\png.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngerror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngget.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngpread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngrio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngrtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngrutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngtrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwrite.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\pngwutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\intel\intel_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\lpng1648\intel\filter_sse2_intrinsics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\dev\lpng1648\png.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\lpng1648\pngconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\BLPConverter\build\x64-release\libpng\pnglibconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\lpng1648\pngpriv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\lpng1648\pngdebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\lpng1648\pnginfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\lpng1648\pngstruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\dev\lpng1648\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B2C48FB9-7A8B-3174-B0A4-05D5F8BBE410}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{96D470C5-0D0F-39F2-8FB2-821C11899131}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
