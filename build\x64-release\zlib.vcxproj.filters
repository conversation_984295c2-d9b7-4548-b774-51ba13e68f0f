﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\dev\zlib-1.3.1\adler32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\compress.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\crc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\deflate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\infback.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\inffast.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\inflate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\inftrees.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\trees.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\uncompr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\zutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\dev\BLPConverter\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{96D470C5-0D0F-39F2-8FB2-821C11899131}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
