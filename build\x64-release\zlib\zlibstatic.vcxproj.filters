﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\dev\zlib-1.3.1\adler32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\compress.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\crc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\deflate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\gzclose.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\gzlib.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\gzread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\gzwrite.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\inflate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\infback.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\inftrees.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\inffast.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\trees.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\uncompr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\dev\zlib-1.3.1\zutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\dev\BLPConverter\build\x64-release\zlib\zconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\zlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\crc32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\deflate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\gzguts.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\inffast.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\inffixed.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\inflate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\inftrees.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\trees.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\dev\zlib-1.3.1\zutil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\dev\zlib-1.3.1\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{B2C48FB9-7A8B-3174-B0A4-05D5F8BBE410}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{96D470C5-0D0F-39F2-8FB2-821C11899131}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
