﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="$(PngSrcDir)\arm\arm_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\arm\filter_neon_intrinsics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\arm\palette_neon_intrinsics.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\png.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngerror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngget.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngpread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngrio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngrtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngrutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngtrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngwio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngwrite.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngwtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(PngSrcDir)\pngwutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <!-- insert here -->
  <!--ItemGroup>
    <ClInclude Include="$(PngSrcDir)\pngconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(PngSrcDir)\pngdebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(PngSrcDir)\png.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(PngSrcDir)\pnginfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(PngSrcDir)\pnglibconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(PngSrcDir)\pngpriv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(PngSrcDir)\pngstruct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup-->
  <ItemGroup>
    <ResourceCompile Include="$(PngSrcDir)\scripts\pngwin.rc" />
    <ResourceCompile Include="libpng.rc" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{20eae163-7543-4266-afae-402692097581}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{b95ccbab-8c2f-4eb7-9908-43ad5877076b}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="resource.h" />
  </ItemGroup>
</Project>