﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="$(SquishSrcDir)\alpha.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\clusterfit.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\colourblock.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\colourfit.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\colourset.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\maths.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\rangefit.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\singlecolourfit.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(SquishSrcDir)\squish.cpp" >
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="$(SquishSrcDir)\alpha.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\clusterfit.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\colourblock.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\colourfit.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\colourset.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\maths.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\rangefit.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\simd.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\simd_float.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\simd_sse.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\simd_ve.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\singlecolourfit.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\singlecolourlookup.inl" >
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="$(SquishSrcDir)\squish.h" >
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{5F130A4F-33AD-3DCC-B8D9-1C4BF01B33F0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{092EE703-1E7A-3433-A596-142D2E37423C}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>