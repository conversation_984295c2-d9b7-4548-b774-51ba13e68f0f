﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ef472a72-ff0d-4738-9301-34ae48cb2827}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{f5cb85a6-0dcd-4718-9bf6-8e1ae1410617}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="$(ZLibSrcDir)\adler32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\compress.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\crc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\deflate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\infback.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\inffast.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\inflate.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\inftrees.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\trees.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\uncompr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="$(ZLibSrcDir)\zutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <!-- insert here -->
</Project>